# UIOrbit - AI UI/UX Super Copilot

**"Generate UI like a Senior + Animate like a Pro"**

UIOrbit is a cutting-edge VS Code extension that revolutionizes frontend development by acting as an AI-native UI/UX Super Copilot. Generate modern, animated, beautifully crafted, and fully functional UI components straight from natural language prompts.

## ✨ Features

- **🎨 Modern Design Systems**: Seamlessly integrates with TailwindCSS and ShadCN UI
- **💫 Micro Animations**: Generates smooth, production-ready animations with Framer Motion
- **🌘 Dark Mode Ready**: Built-in dark mode logic and styling
- **📱 Fully Responsive**: Intelligent grid/flex layouts for all devices
- **🧠 Accessibility Built-In**: ARIA labels, keyboard navigation, semantic HTML
- **🧊 Trendy Visuals**: Modern aesthetics like glassmorphism, gradients, and shadows
- **🤖 Agent Mode**: AI that understands design trends and generates contextually appropriate UI
- **🔄 Multi-File Generation**: Intelligent component breakdown with proper file structure

## 🚀 Quick Start

1. Install the UIOrbit extension from the VS Code Marketplace
2. Get your API key from [UIOrbit Dashboard](https://uiorbit.dev/dashboard)
3. Open a React/Next.js project in VS Code
4. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac) and type "UIOrbit: Generate UI Component"
5. Describe your UI component and select your preferences
6. Watch as UIOrbit generates beautiful, production-ready code!

## 📝 Example Prompts

- "Animated pricing table with toggle between monthly/yearly plans, glassmorphism, and dark mode support"
- "Hero section for a SaaS landing page with blurred background image and gradient text"
- "Sidebar with collapsible menu items, SVG icons, and keyboard navigation"
- "User profile page with avatar, stats cards, and theme toggle"

## ⚙️ Configuration

Configure UIOrbit in VS Code settings:

- `uiorbit.apiKey`: Your UIOrbit API key
- `uiorbit.defaultFramework`: Default framework (React/Next.js)
- `uiorbit.defaultStyling`: Default styling system (TailwindCSS/ShadCN)
- `uiorbit.defaultAnimation`: Default animation library (Framer Motion/GSAP)

## 🛠️ Requirements

- VS Code 1.74.0 or higher
- Node.js project with React or Next.js
- UIOrbit API key (get one at [uiorbit.dev](https://uiorbit.dev))

## 📚 Documentation

Visit [docs.uiorbit.dev](https://docs.uiorbit.dev) for detailed documentation, tutorials, and examples.

## 🐛 Issues & Feedback

Found a bug or have a feature request? Please open an issue on our [GitHub repository](https://github.com/uiorbit/vscode-extension).

## 📄 License

This extension is licensed under the MIT License.

---

**Made with ❤️ by the UIOrbit team**
