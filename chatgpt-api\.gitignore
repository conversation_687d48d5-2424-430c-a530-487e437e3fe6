# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

*.swp
.idea

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# local env files
.env
.env.local
.env.build
.env.development.local
.env.test.local
.env.production.local

# data dumps
out/
