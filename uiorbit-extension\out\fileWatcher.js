"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileWatcher = void 0;
const vscode = require("vscode");
const path = require("path");
const codeAnalyzer_1 = require("./codeAnalyzer");
class FileWatcher {
    constructor(context, database) {
        this.context = context;
        this.watchers = [];
        this.analyzer = null;
        this.isIndexing = false;
        this.indexingQueue = [];
        this.database = database;
    }
    async startWatching() {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders) {
            return;
        }
        for (const folder of workspaceFolders) {
            await this.watchWorkspace(folder);
        }
        // Initial indexing
        await this.performInitialIndexing();
    }
    async watchWorkspace(workspaceFolder) {
        const workspacePath = workspaceFolder.uri.fsPath;
        this.analyzer = new codeAnalyzer_1.CodeAnalyzer(workspacePath);
        // Watch TypeScript/JavaScript files
        const tsWatcher = vscode.workspace.createFileSystemWatcher(new vscode.RelativePattern(workspaceFolder, '**/*.{ts,tsx,js,jsx}'));
        // Watch configuration files
        const configWatcher = vscode.workspace.createFileSystemWatcher(new vscode.RelativePattern(workspaceFolder, '**/*.{json,config.js,config.ts}'));
        // File created
        tsWatcher.onDidCreate(async (uri) => {
            await this.handleFileChange(uri, 'created');
        });
        // File changed
        tsWatcher.onDidChange(async (uri) => {
            await this.handleFileChange(uri, 'changed');
        });
        // File deleted
        tsWatcher.onDidDelete(async (uri) => {
            await this.handleFileChange(uri, 'deleted');
        });
        // Config file changes
        configWatcher.onDidChange(async (uri) => {
            if (this.isImportantConfigFile(uri.fsPath)) {
                await this.reindexProject();
            }
        });
        this.watchers.push(tsWatcher, configWatcher);
        this.context.subscriptions.push(tsWatcher, configWatcher);
    }
    async handleFileChange(uri, changeType) {
        const filePath = uri.fsPath;
        // Skip if file should be ignored
        if (this.shouldIgnoreFile(filePath)) {
            return;
        }
        try {
            switch (changeType) {
                case 'created':
                case 'changed':
                    await this.indexFile(filePath);
                    break;
                case 'deleted':
                    await this.database.deleteCodeChunksByFile(filePath);
                    break;
            }
            // Update status bar
            this.updateStatusBar(`UIOrbit: Indexed ${path.basename(filePath)}`);
        }
        catch (error) {
            console.error(`Error handling file change for ${filePath}:`, error);
        }
    }
    async indexFile(filePath) {
        if (!this.analyzer) {
            return;
        }
        try {
            // Read file content
            const document = await vscode.workspace.openTextDocument(filePath);
            const sourceFile = this.analyzer['program']?.getSourceFile(filePath);
            if (sourceFile) {
                // Analyze the file
                const chunks = await this.analyzer.analyzeFile(sourceFile);
                // Delete existing chunks for this file
                await this.database.deleteCodeChunksByFile(filePath);
                // Insert new chunks
                for (const chunk of chunks) {
                    await this.database.insertCodeChunk(chunk);
                }
                console.log(`Indexed ${chunks.length} chunks from ${path.basename(filePath)}`);
            }
        }
        catch (error) {
            console.error(`Error indexing file ${filePath}:`, error);
        }
    }
    async performInitialIndexing() {
        if (this.isIndexing) {
            return;
        }
        this.isIndexing = true;
        try {
            // Show progress
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: "UIOrbit",
                cancellable: false
            }, async (progress) => {
                progress.report({ increment: 0, message: "Analyzing codebase..." });
                if (!this.analyzer) {
                    return;
                }
                // Analyze entire project
                const result = await this.analyzer.analyzeProject();
                progress.report({ increment: 50, message: "Indexing components..." });
                // Store chunks in database
                for (let i = 0; i < result.chunks.length; i++) {
                    const chunk = result.chunks[i];
                    await this.database.insertCodeChunk(chunk);
                    // Update progress
                    const progressPercent = 50 + (i / result.chunks.length) * 40;
                    progress.report({
                        increment: progressPercent,
                        message: `Indexing ${chunk.name}...`
                    });
                }
                // Store project context
                const workspacePath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
                if (workspacePath) {
                    await this.database.updateProjectContext(workspacePath, result.projectContext);
                }
                progress.report({ increment: 100, message: "Indexing complete!" });
                // Show completion message
                vscode.window.showInformationMessage(`🎉 UIOrbit: Indexed ${result.chunks.length} components and patterns!`);
            });
        }
        catch (error) {
            console.error('Error during initial indexing:', error);
            vscode.window.showErrorMessage(`UIOrbit indexing failed: ${error}`);
        }
        finally {
            this.isIndexing = false;
        }
    }
    async reindexProject() {
        // Clear existing data
        const workspacePath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (workspacePath) {
            // Note: In a real implementation, you'd want to clear the database more selectively
            console.log('Project configuration changed, reindexing...');
            await this.performInitialIndexing();
        }
    }
    shouldIgnoreFile(filePath) {
        const ignoredPatterns = [
            'node_modules',
            '.git',
            'dist',
            'build',
            '.next',
            'coverage',
            '.nyc_output',
            '.vscode',
            '.idea'
        ];
        const ignoredExtensions = [
            '.d.ts',
            '.map',
            '.min.js',
            '.min.css'
        ];
        // Check patterns
        for (const pattern of ignoredPatterns) {
            if (filePath.includes(pattern)) {
                return true;
            }
        }
        // Check extensions
        for (const ext of ignoredExtensions) {
            if (filePath.endsWith(ext)) {
                return true;
            }
        }
        // Check .uiorbitignore file (future enhancement)
        return false;
    }
    isImportantConfigFile(filePath) {
        const importantConfigs = [
            'package.json',
            'tsconfig.json',
            'jsconfig.json',
            'tailwind.config.js',
            'tailwind.config.ts',
            'next.config.js',
            'next.config.ts'
        ];
        const fileName = path.basename(filePath);
        return importantConfigs.includes(fileName);
    }
    updateStatusBar(message) {
        // Create or update status bar item
        const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        statusBarItem.text = message;
        statusBarItem.show();
        // Hide after 3 seconds
        setTimeout(() => {
            statusBarItem.hide();
            statusBarItem.dispose();
        }, 3000);
    }
    async getRelevantContext(prompt, limit = 5) {
        try {
            // Search for relevant code chunks
            const chunks = await this.database.searchCodeChunks(prompt, limit);
            // Also get recent components
            const recentComponents = await this.database.getCodeChunksByType('component');
            // Combine and deduplicate
            const allChunks = [...chunks, ...recentComponents.slice(0, 3)];
            const uniqueChunks = allChunks.filter((chunk, index, self) => index === self.findIndex(c => c.id === chunk.id));
            return uniqueChunks.slice(0, limit);
        }
        catch (error) {
            console.error('Error getting relevant context:', error);
            return [];
        }
    }
    async getProjectSummary() {
        try {
            const workspacePath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            if (!workspacePath) {
                return 'No workspace open';
            }
            const context = await this.database.getProjectContext(workspacePath);
            const components = await this.database.getCodeChunksByType('component');
            const hooks = await this.database.getCodeChunksByType('hook');
            return `Project: ${context?.framework || 'Unknown'} with ${context?.styling || 'Unknown'} styling
Components: ${components.length} (${components.slice(0, 3).map(c => c.name).join(', ')})
Hooks: ${hooks.length} custom hooks
Patterns: ${context?.patterns?.join(', ') || 'None detected'}`;
        }
        catch (error) {
            console.error('Error getting project summary:', error);
            return 'Error analyzing project';
        }
    }
    dispose() {
        this.watchers.forEach(watcher => watcher.dispose());
        this.watchers = [];
    }
}
exports.FileWatcher = FileWatcher;
//# sourceMappingURL=fileWatcher.js.map