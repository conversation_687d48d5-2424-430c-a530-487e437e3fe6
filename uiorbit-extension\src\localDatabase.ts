import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { Database } from 'sqlite3';

export interface CodeChunk {
    id: string;
    filePath: string;
    content: string;
    type: 'component' | 'hook' | 'utility' | 'style' | 'type';
    name: string;
    startLine: number;
    endLine: number;
    dependencies: string[];
    metadata: {
        framework?: string;
        styling?: string;
        hasProps?: boolean;
        hasState?: boolean;
        isExported?: boolean;
    };
    embedding?: number[];
    lastModified: number;
}

export interface ProjectContext {
    framework: 'react' | 'next' | 'vue' | 'angular';
    styling: 'tailwind' | 'styled-components' | 'css-modules' | 'scss';
    components: string[];
    patterns: string[];
    designSystem?: string;
}

export class LocalVectorDatabase {
    private db: Database | null = null;
    private dbPath: string;
    private context: vscode.ExtensionContext;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.dbPath = path.join(context.globalStorageUri?.fsPath || context.extensionPath, 'uiorbit.db');
    }

    async initialize(): Promise<void> {
        return new Promise((resolve, reject) => {
            // Ensure directory exists
            const dbDir = path.dirname(this.dbPath);
            if (!fs.existsSync(dbDir)) {
                fs.mkdirSync(dbDir, { recursive: true });
            }

            this.db = new Database(this.dbPath, (err) => {
                if (err) {
                    reject(err);
                    return;
                }

                this.createTables()
                    .then(() => resolve())
                    .catch(reject);
            });
        });
    }

    private async createTables(): Promise<void> {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('Database not initialized'));
                return;
            }

            const createTablesSQL = `
                -- Code chunks table
                CREATE TABLE IF NOT EXISTS code_chunks (
                    id TEXT PRIMARY KEY,
                    file_path TEXT NOT NULL,
                    content TEXT NOT NULL,
                    type TEXT NOT NULL,
                    name TEXT NOT NULL,
                    start_line INTEGER NOT NULL,
                    end_line INTEGER NOT NULL,
                    dependencies TEXT, -- JSON array
                    metadata TEXT, -- JSON object
                    last_modified INTEGER NOT NULL,
                    created_at INTEGER DEFAULT (strftime('%s', 'now'))
                );

                -- Project context table
                CREATE TABLE IF NOT EXISTS project_context (
                    id INTEGER PRIMARY KEY,
                    workspace_path TEXT UNIQUE NOT NULL,
                    framework TEXT,
                    styling TEXT,
                    components TEXT, -- JSON array
                    patterns TEXT, -- JSON array
                    design_system TEXT,
                    last_updated INTEGER DEFAULT (strftime('%s', 'now'))
                );

                -- Embeddings table (for future vector search)
                CREATE TABLE IF NOT EXISTS embeddings (
                    chunk_id TEXT PRIMARY KEY,
                    embedding BLOB,
                    FOREIGN KEY (chunk_id) REFERENCES code_chunks (id)
                );

                -- Indexes for performance
                CREATE INDEX IF NOT EXISTS idx_code_chunks_file_path ON code_chunks(file_path);
                CREATE INDEX IF NOT EXISTS idx_code_chunks_type ON code_chunks(type);
                CREATE INDEX IF NOT EXISTS idx_code_chunks_name ON code_chunks(name);
                CREATE INDEX IF NOT EXISTS idx_code_chunks_modified ON code_chunks(last_modified);
            `;

            this.db.exec(createTablesSQL, (err) => {
                if (err) {
                    reject(err);
                } else {
                    resolve();
                }
            });
        });
    }

    async insertCodeChunk(chunk: CodeChunk): Promise<void> {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('Database not initialized'));
                return;
            }

            const sql = `
                INSERT OR REPLACE INTO code_chunks 
                (id, file_path, content, type, name, start_line, end_line, dependencies, metadata, last_modified)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            this.db.run(sql, [
                chunk.id,
                chunk.filePath,
                chunk.content,
                chunk.type,
                chunk.name,
                chunk.startLine,
                chunk.endLine,
                JSON.stringify(chunk.dependencies),
                JSON.stringify(chunk.metadata),
                chunk.lastModified
            ], (err) => {
                if (err) {
                    reject(err);
                } else {
                    resolve();
                }
            });
        });
    }

    async getCodeChunksByType(type: string): Promise<CodeChunk[]> {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('Database not initialized'));
                return;
            }

            const sql = 'SELECT * FROM code_chunks WHERE type = ? ORDER BY last_modified DESC';
            
            this.db.all(sql, [type], (err, rows: any[]) => {
                if (err) {
                    reject(err);
                } else {
                    const chunks = rows.map(row => ({
                        id: row.id,
                        filePath: row.file_path,
                        content: row.content,
                        type: row.type,
                        name: row.name,
                        startLine: row.start_line,
                        endLine: row.end_line,
                        dependencies: JSON.parse(row.dependencies || '[]'),
                        metadata: JSON.parse(row.metadata || '{}'),
                        lastModified: row.last_modified
                    }));
                    resolve(chunks);
                }
            });
        });
    }

    async searchCodeChunks(query: string, limit: number = 10): Promise<CodeChunk[]> {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('Database not initialized'));
                return;
            }

            // Simple text search for now, can be enhanced with vector search later
            const sql = `
                SELECT * FROM code_chunks 
                WHERE content LIKE ? OR name LIKE ? 
                ORDER BY last_modified DESC 
                LIMIT ?
            `;
            
            const searchTerm = `%${query}%`;
            
            this.db.all(sql, [searchTerm, searchTerm, limit], (err, rows: any[]) => {
                if (err) {
                    reject(err);
                } else {
                    const chunks = rows.map(row => ({
                        id: row.id,
                        filePath: row.file_path,
                        content: row.content,
                        type: row.type,
                        name: row.name,
                        startLine: row.start_line,
                        endLine: row.end_line,
                        dependencies: JSON.parse(row.dependencies || '[]'),
                        metadata: JSON.parse(row.metadata || '{}'),
                        lastModified: row.last_modified
                    }));
                    resolve(chunks);
                }
            });
        });
    }

    async updateProjectContext(workspacePath: string, context: ProjectContext): Promise<void> {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('Database not initialized'));
                return;
            }

            const sql = `
                INSERT OR REPLACE INTO project_context 
                (workspace_path, framework, styling, components, patterns, design_system, last_updated)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            `;

            this.db.run(sql, [
                workspacePath,
                context.framework,
                context.styling,
                JSON.stringify(context.components),
                JSON.stringify(context.patterns),
                context.designSystem,
                Date.now()
            ], (err) => {
                if (err) {
                    reject(err);
                } else {
                    resolve();
                }
            });
        });
    }

    async getProjectContext(workspacePath: string): Promise<ProjectContext | null> {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('Database not initialized'));
                return;
            }

            const sql = 'SELECT * FROM project_context WHERE workspace_path = ?';
            
            this.db.get(sql, [workspacePath], (err, row: any) => {
                if (err) {
                    reject(err);
                } else if (row) {
                    const context: ProjectContext = {
                        framework: row.framework,
                        styling: row.styling,
                        components: JSON.parse(row.components || '[]'),
                        patterns: JSON.parse(row.patterns || '[]'),
                        designSystem: row.design_system
                    };
                    resolve(context);
                } else {
                    resolve(null);
                }
            });
        });
    }

    async deleteCodeChunk(id: string): Promise<void> {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('Database not initialized'));
                return;
            }

            const sql = 'DELETE FROM code_chunks WHERE id = ?';
            
            this.db.run(sql, [id], (err) => {
                if (err) {
                    reject(err);
                } else {
                    resolve();
                }
            });
        });
    }

    async deleteCodeChunksByFile(filePath: string): Promise<void> {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('Database not initialized'));
                return;
            }

            const sql = 'DELETE FROM code_chunks WHERE file_path = ?';
            
            this.db.run(sql, [filePath], (err) => {
                if (err) {
                    reject(err);
                } else {
                    resolve();
                }
            });
        });
    }

    async close(): Promise<void> {
        return new Promise((resolve) => {
            if (this.db) {
                this.db.close(() => {
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }
}
