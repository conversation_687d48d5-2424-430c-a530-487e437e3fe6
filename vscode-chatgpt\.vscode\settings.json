{
    "files.exclude": {
        "out": false
    },
    "search.exclude": {
        "out": true
    },
    "typescript.tsc.autoDetect": "off",
    "editor.formatOnSave": true,
    "editor.formatOnPaste": true,
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.codeActionsOnSave": {
        "source.fixAll": true,
        "source.organizeImports": true
    },
    "javascript.format.semicolons": "insert",
    "typescript.format.semicolons": "insert",
    "javascript.preferences.quoteStyle": "double",
    "[typescript]": {
        "editor.defaultFormatter": "vscode.typescript-language-features",
        "typescript.preferences.quoteStyle": "double",
    },
    "[javascript]": {
        "editor.defaultFormatter": "vscode.typescript-language-features"
    },
    "[json]": {
        "editor.defaultFormatter": "vscode.json-language-features"
    },
    "[jsonc]": {
        "editor.defaultFormatter": "vscode.json-language-features"
    },
    "[css]": {
        "editor.defaultFormatter": "vscode.css-language-features"
    },
}