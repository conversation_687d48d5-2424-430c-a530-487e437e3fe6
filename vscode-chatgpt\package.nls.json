{"vscode-chatgpt.freeText.title": "ChatGPT: Ask anything", "vscode-chatgpt.clearSession.title": "ChatGPT: Reset session", "vscode-chatgpt.generateCode.title": "ChatGPT-Codex: Generate code", "vscode-chatgpt.addTests.title": "ChatGPT: Add tests", "vscode-chatgpt.findProblems.title": "ChatGPT: Find bugs", "vscode-chatgpt.optimize.title": "ChatGPT: Optimize", "vscode-chatgpt.explain.title": "ChatGPT: Explain", "vscode-chatgpt.addComments.title": "ChatGPT: Add comments", "vscode-chatgpt.completeCode.title": "ChatGPT: Complete code", "vscode-chatgpt.adhoc.title": "ChatGPT: Ad-hoc prompt", "vscode-chatgpt.customPrompt1.title": "ChatGPT: Custom prompt 1", "vscode-chatgpt.customPrompt2.title": "ChatGPT: Custom prompt 2", "vscode-chatgpt.clearConversation.title": "ChatGPT: Clear conversation", "vscode-chatgpt.exportConversation.title": "ChatGPT: Export conversation", "vscode-chatgpt-view-container.name": "Conversation window", "chatgpt.method.markdownDescription": "Choose your integration preference.", "chatgpt.method.markdownEnumDescriptions1": "Make sure to select the correct model from the settings \n\n- Browser autologin - default ChatGPT model -> `text-davinci-002-render-sha` \n\n- Browser autologin - ChatGPT Plus default model -> `text-davinci-002-render-paid` \n\n- Browser autologin - ChatGPT Plus legacy model -> `text-davinci-002-render-sha`", "chatgpt.method.markdownEnumDescriptions2": "Various text & code completion models are supported including ChatGPT Turbo models. \n\n- GPT3.5 -> i.e. `'chatgpt.gpt3.model': 'gpt-3.5-turbo'`\n\n- GPT3 -> i.e. `'chatgpt.gpt3.model': 'text-davinci-003'`\n\n- Codex -> i.e.`'chatgpt.gpt3.model': 'code-davinci-002'`", "chatgpt.authenticationType.markdownDescription": "Choose your login type to autofill the provided email/password in the authentication flow. \n\n - `OpenAI Authentication` Standard authentication method. Use this if you signed up using your email address instead of Google/Microsoft authentication. \n\n - `Google Authentication` Use Google Authentication to login to OpenAI \n\n - `Microsoft Authentication` Use Microsoft Authentication to login to OpenAI", "chatgpt.emailAddress.description": "[Optional] Your openai.com login email address. Provide this if you want to auto-fill your email address during autologin. You don't have to provide this if you want to fill it during login.", "chatgpt.password.description": "[Optional] Your openai.com login password. Provide this if you want to auto-fill your password during autologin. You don't have to provide this if you want to fill it during login.", "chatgpt.proxyServer.markdownDescription": "[Optional] The proxy server you'd like to use. Supports HTTP proxies only, don't provide the protocol in the setting. Format example: \n\n `authenticated`:`myUsername:<EMAIL>:3001` \n\n `anonymous`: `**************:999` \n\n **Only available for `Browser Auto-login` method**", "chatgpt.chromiumPath.markdownDescription": "The executable path of your Chromium-based browser. i.e. `Chrome`, `Edge` etc. \n\n Unless you override, we use the default paths per Operating System for `Chrome`. \n\n **Windows**\n C:\\\\Program Files\\\\Google\\\\Chrome\\\\Application\\\\chrome.exe \n\n **MAC**\n /Applications/Google Chrome.app/Contents/MacOS/Google Chrome \n\n You can use Chromium based Edge as well. e.g. \n\n **Windows Edge**\n C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe \n\n **You can find the executable path for your preferred browser by going to these URLs: `chrome://version` or `edge://version`**", "chatgpt.gpt3.generateCode-enabled.description": "Enable the code generation context menu item for the selected comment/code for Codex. Only available with code-* models", "chatgpt.promptPrefix.addTests.default": "Implement tests for the following code", "chatgpt.promptPrefix.addTests.description": "The prompt prefix used for adding tests for the selected code", "chatgpt.promptPrefix.addTests-enabled.description": "Enable the prompt prefix used for adding tests for the selected code in the context menu", "chatgpt.promptPrefix.findProblems.default": "Find problems with the following code", "chatgpt.promptPrefix.findProblems.description": "The prompt prefix used for finding problems for the selected code", "chatgpt.promptPrefix.findProblems-enabled.description": "Enable the prompt prefix used for finding problems for the selected code in the context menu", "chatgpt.promptPrefix.optimize.default": "Optimize the following code", "chatgpt.promptPrefix.optimize.description": "The prompt prefix used for optimizing the selected code", "chatgpt.promptPrefix.optimize-enabled.description": "Enable the prompt prefix used for optimizing the selected code in the context menu", "chatgpt.promptPrefix.explain.default": "Explain the following code", "chatgpt.promptPrefix.explain.description": "The prompt prefix used for explaining the selected code", "chatgpt.promptPrefix.explain-enabled.description": "Enable the prompt prefix used for explaining the selected code in the context menu", "chatgpt.promptPrefix.addComments.default": "Add comments for the following code", "chatgpt.promptPrefix.addComments.description": "The prompt prefix used for adding comments for the selected code", "chatgpt.promptPrefix.addComments-enabled.description": "Enable the prompt prefix used for adding comments for the selected code in the context menu", "chatgpt.promptPrefix.completeCode.default": "Complete the following code", "chatgpt.promptPrefix.completeCode.description": "The prompt prefix used for completing the selected code", "chatgpt.promptPrefix.completeCode-enabled.description": "Enable the prompt prefix used for completing the selected code in the context menu", "chatgpt.promptPrefix.customPrompt1.description": "Your custom prompt. It's disabled by default, please set to a custom prompt and enable it if you prefer using customized prompt", "chatgpt.promptPrefix.customPrompt1-enabled.markdownDescription": "Enable the prompt prefix used for your custom prompt. The default value is empty, if you enable this item make sure to set this `chatgpt.promptPrefix.customPrompt1`", "chatgpt.promptPrefix.customPrompt2.description": "Your custom prompt. It's disabled by default, please set to a custom prompt and enable it if you prefer using customized prompt", "chatgpt.promptPrefix.customPrompt2-enabled.markdownDescription": "Enable the prompt prefix used for your custom prompt. The default value is empty, if you enable this item make sure to set this `chatgpt.promptPrefix.customPrompt2`", "chatgpt.promptPrefix.adhoc-enabled.description": "Enable the prompt prefix used for adhoc command for the selected code in the context menu", "chatgpt.gpt3.apiKey.markdownDescription": "OpenAI API key. [Get your API Key from OpenAI](https://beta.openai.com/account/api-keys). \n\n**Please enable OpenAI API Key method to use this setting.**", "chatgpt.gpt3.apiBaseUrl.markdownDescription": "Optional override for the OpenAI API base URL. If you customize it, please make sure you have the same format. e.g. starts with `https://` without a trailing slash. The completions endpoint suffix is added internally, e.g. for reference: `${apiBaseUrl}/v1/completions`", "chatgpt.gpt3.organization.markdownDescription": "OpenAI Organization ID. [Documentation](https://beta.openai.com/docs/api-reference/requesting-organization). \n\n**Please enable OpenAI API Key method to use this setting.**", "chatgpt.gpt3.model.markdownDescription": "OpenAI models to use for your prompts. [Documentation](https://beta.openai.com/docs/models/models). \n\n**If you face 400 Bad Request please make sure you are using the right model for your integration method.**", "chatgpt.gpt3.model.enumItemLabels1": "Browser autologin - default ChatGPT model", "chatgpt.gpt3.model.enumItemLabels2": "Browser autologin - ChatGPT Plus GPT-4", "chatgpt.gpt3.model.enumItemLabels3": "Browser autologin - ChatGPT Plus default model", "chatgpt.gpt3.model.enumItemLabels4": "Browser autologin - ChatGPT Plus legacy model", "chatgpt.gpt3.model.enumItemLabels5": "OpenAI API Key - gpt-3.5-turbo", "chatgpt.gpt3.model.enumItemLabels6": "OpenAI API Key - gpt-3.5-turbo-0301", "chatgpt.gpt3.model.enumItemLabels7": "OpenAI API Key - text-davinci-003", "chatgpt.gpt3.model.enumItemLabels8": "OpenAI API Key - text-curie-001", "chatgpt.gpt3.model.enumItemLabels9": "OpenAI API Key - text-babbage-001", "chatgpt.gpt3.model.enumItemLabels10": "OpenAI API Key - text-ada-001", "chatgpt.gpt3.model.enumItemLabels11": "OpenAI API Key - code-davinci-002", "chatgpt.gpt3.model.enumItemLabels12": "OpenAI API Key - code-cushman-001", "chatgpt.gpt3.model.markdownEnumDescriptions1": "Free Tier ChatGPT model that's used in chat.openai.com", "chatgpt.gpt3.model.markdownEnumDescriptions2": "ChatGPT Plus subscription GPT-4 model that's used in chat.openai.com. This requires a subscription on OpenAI side, please make sure you are eligible to use this model. \n\n**More capable than any GPT-3.5 model, able to do more complex tasks, and optimized for chat. Available to ChatGPT Plus users**", "chatgpt.gpt3.model.markdownEnumDescriptions3": "ChatGPT Plus subscription default model that's used in chat.openai.com. This requires a subscription on OpenAI side, please make sure you are eligible to use this model. \n\n**Optimized for speed, currently available to Plus users**", "chatgpt.gpt3.model.markdownEnumDescriptions4": "ChatGPT Plus subscription legacy model that's used in chat.openai.com. This requires a subscription on OpenAI side, please make sure you are eligible to use this model. \n\n**The previous ChatGPT Plus model**", "chatgpt.gpt3.model.markdownEnumDescriptions5": "Most capable GPT-3.5 model and optimized for chat at 1/10th the cost of `text-davinci-003`. Will be updated with our latest model iteration.", "chatgpt.gpt3.model.markdownEnumDescriptions6": "Snapshot of `gpt-3.5-turbo` from March 1st 2023. Unlike gpt-3.5-turbo, this model will not receive updates, and will only be supported for a three month period ending on June 1st 2023.", "chatgpt.gpt3.maxTokens.markdownDescription": "The maximum number of tokens to generate in the completion. \n\nThe token count of your prompt plus max_tokens cannot exceed the model's context length. Most models have a context length of 2048 tokens (except for the newest models, which support 4096). [Documentation](https://beta.openai.com/docs/api-reference/completions/create#completions/create-max_tokens) \n\n**Please enable OpenAI API Key method to use this setting.**", "chatgpt.gpt3.temperature.markdownDescription": "What sampling temperature to use. Higher values means the model will take more risks. Try 0.9 for more creative applications, and 0 (argmax sampling) for ones with a well-defined answer.\n\nIt is recommended altering this or top_p but not both. [Documentation](https://beta.openai.com/docs/api-reference/completions/create#completions/create-temperature) \n\n**Please enable OpenAI API Key method to use this setting.**", "chatgpt.gpt3.top_p.markdownDescription": "An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered. \n\nIt is recommended altering this or temperature but not both. [Documentation](https://beta.openai.com/docs/api-reference/completions/create#completions/create-top_p) \n\n**Please enable OpenAI API Key method to use this setting.**", "chatgpt.response.showNotification.description": "Choose whether you'd like to receive a notification when ChatGPT bot responds to your query.", "chatgpt.response.autoScroll.description": "Whenever there is a new question or response added to the conversation window, extension will automatically scroll to the bottom. You can change that behavior by disabling this setting.", "chatgpt.telemetry.disable.markdownDescription": "Specify if you want to disable the telemetry. This extension also respects your default vs-code telemetry setting `telemetry.telemetryLevel`. We check both settings for telemetry. **Important**: No user data is tracked, we only use telemetry to see what is used, and what isn't. This allows us to make accurate decisions on what to add or enhance to the extension."}