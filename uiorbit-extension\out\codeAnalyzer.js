"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeAnalyzer = void 0;
const ts = require("typescript");
const path = require("path");
const fs = require("fs");
class CodeAnalyzer {
    constructor(workspacePath) {
        this.workspacePath = workspacePath;
        this.program = null;
        this.checker = null;
    }
    async analyzeProject() {
        const configPath = this.findTsConfig();
        const chunks = [];
        let projectContext = {
            framework: 'react',
            styling: 'tailwind',
            components: [],
            patterns: []
        };
        if (configPath) {
            // Create TypeScript program
            const configFile = ts.readConfigFile(configPath, ts.sys.readFile);
            const parsedConfig = ts.parseJsonConfigFileContent(configFile.config, ts.sys, path.dirname(configPath));
            this.program = ts.createProgram(parsedConfig.fileNames, parsedConfig.options);
            this.checker = this.program.getTypeChecker();
            // Analyze each source file
            for (const sourceFile of this.program.getSourceFiles()) {
                if (!sourceFile.isDeclarationFile && this.isProjectFile(sourceFile.fileName)) {
                    const fileChunks = await this.analyzeFile(sourceFile);
                    chunks.push(...fileChunks);
                }
            }
            // Analyze project context
            projectContext = await this.analyzeProjectContext(chunks);
        }
        return { chunks, projectContext };
    }
    async analyzeFile(sourceFile) {
        const chunks = [];
        const filePath = sourceFile.fileName;
        const content = sourceFile.getFullText();
        // Visit all nodes in the file
        const visit = (node) => {
            // Analyze React components
            if (this.isReactComponent(node)) {
                const chunk = this.createComponentChunk(node, sourceFile, content);
                if (chunk)
                    chunks.push(chunk);
            }
            // Analyze custom hooks
            else if (this.isCustomHook(node)) {
                const chunk = this.createHookChunk(node, sourceFile, content);
                if (chunk)
                    chunks.push(chunk);
            }
            // Analyze utility functions
            else if (this.isUtilityFunction(node)) {
                const chunk = this.createUtilityChunk(node, sourceFile, content);
                if (chunk)
                    chunks.push(chunk);
            }
            // Analyze type definitions
            else if (this.isTypeDefinition(node)) {
                const chunk = this.createTypeChunk(node, sourceFile, content);
                if (chunk)
                    chunks.push(chunk);
            }
            ts.forEachChild(node, visit);
        };
        visit(sourceFile);
        return chunks;
    }
    isReactComponent(node) {
        // Function component
        if (ts.isFunctionDeclaration(node) || ts.isArrowFunction(node) || ts.isFunctionExpression(node)) {
            const name = this.getNodeName(node);
            if (name && /^[A-Z]/.test(name)) {
                // Check if it returns JSX
                return this.returnsJSX(node);
            }
        }
        // Class component
        if (ts.isClassDeclaration(node)) {
            const name = node.name?.text;
            if (name && /^[A-Z]/.test(name)) {
                // Check if extends React.Component
                return this.extendsReactComponent(node);
            }
        }
        return false;
    }
    isCustomHook(node) {
        if (ts.isFunctionDeclaration(node) || ts.isArrowFunction(node) || ts.isFunctionExpression(node)) {
            const name = this.getNodeName(node);
            return name ? name.startsWith('use') && /^use[A-Z]/.test(name) : false;
        }
        return false;
    }
    isUtilityFunction(node) {
        if (ts.isFunctionDeclaration(node) || ts.isArrowFunction(node) || ts.isFunctionExpression(node)) {
            const name = this.getNodeName(node);
            return name ? /^[a-z]/.test(name) && !name.startsWith('use') : false;
        }
        return false;
    }
    isTypeDefinition(node) {
        return ts.isInterfaceDeclaration(node) || ts.isTypeAliasDeclaration(node) || ts.isEnumDeclaration(node);
    }
    createComponentChunk(node, sourceFile, content) {
        const name = this.getNodeName(node);
        if (!name)
            return null;
        const { start, end } = this.getNodeRange(node, sourceFile);
        const nodeText = content.substring(node.getFullStart(), node.getEnd());
        return {
            id: `${sourceFile.fileName}:${name}:${start}`,
            filePath: sourceFile.fileName,
            content: nodeText,
            type: 'component',
            name,
            startLine: start,
            endLine: end,
            dependencies: this.extractDependencies(node, sourceFile),
            metadata: {
                framework: 'react',
                hasProps: this.hasProps(node),
                hasState: this.hasState(node),
                isExported: this.isExported(node, sourceFile)
            },
            lastModified: Date.now()
        };
    }
    createHookChunk(node, sourceFile, content) {
        const name = this.getNodeName(node);
        if (!name)
            return null;
        const { start, end } = this.getNodeRange(node, sourceFile);
        const nodeText = content.substring(node.getFullStart(), node.getEnd());
        return {
            id: `${sourceFile.fileName}:${name}:${start}`,
            filePath: sourceFile.fileName,
            content: nodeText,
            type: 'hook',
            name,
            startLine: start,
            endLine: end,
            dependencies: this.extractDependencies(node, sourceFile),
            metadata: {
                framework: 'react',
                isExported: this.isExported(node, sourceFile)
            },
            lastModified: Date.now()
        };
    }
    createUtilityChunk(node, sourceFile, content) {
        const name = this.getNodeName(node);
        if (!name)
            return null;
        const { start, end } = this.getNodeRange(node, sourceFile);
        const nodeText = content.substring(node.getFullStart(), node.getEnd());
        return {
            id: `${sourceFile.fileName}:${name}:${start}`,
            filePath: sourceFile.fileName,
            content: nodeText,
            type: 'utility',
            name,
            startLine: start,
            endLine: end,
            dependencies: this.extractDependencies(node, sourceFile),
            metadata: {
                isExported: this.isExported(node, sourceFile)
            },
            lastModified: Date.now()
        };
    }
    createTypeChunk(node, sourceFile, content) {
        const name = this.getNodeName(node);
        if (!name)
            return null;
        const { start, end } = this.getNodeRange(node, sourceFile);
        const nodeText = content.substring(node.getFullStart(), node.getEnd());
        return {
            id: `${sourceFile.fileName}:${name}:${start}`,
            filePath: sourceFile.fileName,
            content: nodeText,
            type: 'type',
            name,
            startLine: start,
            endLine: end,
            dependencies: [],
            metadata: {
                isExported: this.isExported(node, sourceFile)
            },
            lastModified: Date.now()
        };
    }
    getNodeName(node) {
        if (ts.isFunctionDeclaration(node) && node.name) {
            return node.name.text;
        }
        if (ts.isClassDeclaration(node) && node.name) {
            return node.name.text;
        }
        if (ts.isInterfaceDeclaration(node)) {
            return node.name.text;
        }
        if (ts.isTypeAliasDeclaration(node)) {
            return node.name.text;
        }
        if (ts.isVariableDeclaration(node) && ts.isIdentifier(node.name)) {
            return node.name.text;
        }
        return null;
    }
    getNodeRange(node, sourceFile) {
        const start = sourceFile.getLineAndCharacterOfPosition(node.getStart()).line + 1;
        const end = sourceFile.getLineAndCharacterOfPosition(node.getEnd()).line + 1;
        return { start, end };
    }
    extractDependencies(node, sourceFile) {
        const dependencies = [];
        // Extract import statements
        sourceFile.statements.forEach(statement => {
            if (ts.isImportDeclaration(statement) && statement.moduleSpecifier) {
                const moduleSpecifier = statement.moduleSpecifier.text;
                dependencies.push(moduleSpecifier);
            }
        });
        return dependencies;
    }
    hasProps(node) {
        // Check if function has parameters (props)
        if (ts.isFunctionDeclaration(node) || ts.isFunctionExpression(node) || ts.isArrowFunction(node)) {
            return node.parameters.length > 0;
        }
        return false;
    }
    hasState(node) {
        // Simple check for useState hook usage
        const nodeText = node.getFullText();
        return nodeText.includes('useState') || nodeText.includes('useReducer');
    }
    isExported(node, sourceFile) {
        // Check if node has export modifier
        if (ts.canHaveModifiers(node)) {
            const modifiers = ts.getModifiers(node);
            return modifiers?.some(mod => mod.kind === ts.SyntaxKind.ExportKeyword) || false;
        }
        return false;
    }
    returnsJSX(node) {
        const nodeText = node.getFullText();
        return nodeText.includes('jsx') || nodeText.includes('<') || nodeText.includes('React.createElement');
    }
    extendsReactComponent(node) {
        if (node.heritageClauses) {
            for (const clause of node.heritageClauses) {
                for (const type of clause.types) {
                    const typeName = type.expression.getText();
                    if (typeName.includes('Component') || typeName.includes('PureComponent')) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
    async analyzeProjectContext(chunks) {
        const components = chunks.filter(c => c.type === 'component').map(c => c.name);
        const patterns = [];
        // Detect framework
        let framework = 'react';
        if (this.hasNextJsConfig()) {
            framework = 'next';
        }
        // Detect styling approach
        let styling = 'tailwind';
        if (this.hasTailwindConfig()) {
            styling = 'tailwind';
        }
        else if (this.hasStyledComponents(chunks)) {
            styling = 'styled-components';
        }
        return {
            framework,
            styling,
            components,
            patterns
        };
    }
    findTsConfig() {
        const possiblePaths = [
            path.join(this.workspacePath, 'tsconfig.json'),
            path.join(this.workspacePath, 'jsconfig.json')
        ];
        for (const configPath of possiblePaths) {
            if (fs.existsSync(configPath)) {
                return configPath;
            }
        }
        return null;
    }
    isProjectFile(fileName) {
        return fileName.startsWith(this.workspacePath) &&
            !fileName.includes('node_modules') &&
            !fileName.includes('.d.ts') &&
            (fileName.endsWith('.ts') || fileName.endsWith('.tsx') ||
                fileName.endsWith('.js') || fileName.endsWith('.jsx'));
    }
    hasNextJsConfig() {
        return fs.existsSync(path.join(this.workspacePath, 'next.config.js')) ||
            fs.existsSync(path.join(this.workspacePath, 'next.config.ts'));
    }
    hasTailwindConfig() {
        return fs.existsSync(path.join(this.workspacePath, 'tailwind.config.js')) ||
            fs.existsSync(path.join(this.workspacePath, 'tailwind.config.ts'));
    }
    hasStyledComponents(chunks) {
        return chunks.some(chunk => chunk.dependencies.includes('styled-components') ||
            chunk.content.includes('styled.'));
    }
}
exports.CodeAnalyzer = CodeAnalyzer;
//# sourceMappingURL=codeAnalyzer.js.map