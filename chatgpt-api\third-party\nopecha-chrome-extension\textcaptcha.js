(async()=>{async function r(t){function c(a){return new Promise(t=>{const e=new Image;e.onload=()=>t(e),e.src=function(t){let e=t.style.backgroundImage;return e&&((t=e.trim().match(/(?!^)".*?"/g))&&0!==t.length||(e=null),e=t[0].replaceAll('"',"")),e}(a)})}try{return(await async function(t){var e=document.querySelector(t);if(e instanceof HTMLCanvasElement)return e;let a;if(a=e instanceof HTMLImageElement?e:await c(e))return(e=document.createElement("canvas")).width=a.naturalWidth,e.height=a.naturalHeight,e.getContext("2d").drawImage(a,0,0),e;throw Error("failed to get image element for "+t)}(t)).toDataURL("image/jpeg").split(";base64,")[1]}catch(t){return null}}let l=null;async function t(){var t,e,a,c,n=(t=500,await new Promise(e=>{let a=!1;const c=setInterval(async()=>{if(!a){a=!0;var t=await BG.exec("Settings.get");if(t&&t.textcaptcha_auto_solve){t=await r(t.textcaptcha_image_selector);if(t&&l!==t)return l=t,clearInterval(c),a=!1,e({image_data:t})}a=!1}},t)}))["image_data"],i=await BG.exec("Settings.get");i&&i.enabled&&i.textcaptcha_auto_solve&&(c=Time.time(),{job_id:e,data:n}=await NopeCHA.post({captcha_type:IS_DEVELOPMENT?"textcaptcha_dev":"textcaptcha",image_data:[n],key:i.key}),n)&&(a=(a=parseInt(i.textcaptcha_solve_delay_time))||100,0<(a=i.textcaptcha_solve_delay?a-(Time.time()-c):0)&&await Time.sleep(a),n)&&0<n.length&&(c=document.querySelector(i.textcaptcha_input_selector))&&!c.value&&(c.value=n[0])}for(;;){await Time.sleep(1e3);var e,a=await BG.exec("Settings.get");a&&a.enabled&&(e=await Location.hostname(),a.disabled_hosts.includes(e)||a.textcaptcha_auto_solve&&function(t){try{var e;if(t?.textcaptcha_image_selector&&t?.textcaptcha_input_selector)return document.querySelector(t.textcaptcha_image_selector)&&!(!(e=document.querySelector(t.textcaptcha_input_selector))||e.value)}catch(t){}}(a)&&await t())}})();
