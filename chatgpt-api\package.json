{"name": "chatgpt", "version": "3.3.7", "description": "Node.js client for the unofficial ChatGPT API.", "author": "<PERSON> <<EMAIL>>", "repository": "transitive-bullshit/chatgpt-api", "license": "MIT", "type": "module", "source": "./src/index.ts", "types": "./build/index.d.ts", "exports": {".": {"import": "./build/index.js", "types": "./build/index.d.ts", "default": "./build/index.js"}}, "files": ["build", "third-party"], "engines": {"node": ">=18"}, "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "del build", "prebuild": "run-s clean", "predev": "run-s clean", "pretest": "run-s build", "docs": "typedoc", "prepare": "husky install", "pre-commit": "lint-staged", "test": "run-p test:*", "test:prettier": "prettier '**/*.{js,jsx,ts,tsx}' --check"}, "dependencies": {"delay": "^5.0.0", "eventsource-parser": "^0.0.5", "expiry-map": "^2.0.0", "html-to-md": "^0.8.3", "p-timeout": "^6.0.0", "puppeteer-extra": "^3.3.4", "puppeteer-extra-plugin-recaptcha": "npm:@fisch0920/puppeteer-extra-plugin-recaptcha@^3.6.6", "puppeteer-extra-plugin-stealth": "^2.11.1", "random": "^4.1.0", "remark": "^14.0.2", "strip-markdown": "^5.0.0", "uuid": "^9.0.0"}, "devDependencies": {"@trivago/prettier-plugin-sort-imports": "^4.0.0", "@types/node": "^18.11.9", "@types/uuid": "^9.0.0", "ava": "^5.1.0", "del-cli": "^5.0.0", "dotenv-safe": "^8.2.0", "husky": "^8.0.2", "lint-staged": "^13.0.3", "npm-run-all": "^4.1.5", "ora": "^6.1.2", "prettier": "^2.8.0", "puppeteer": "^19.4.0", "tsup": "^6.5.0", "tsx": "^3.12.1", "typedoc": "^0.23.21", "typedoc-plugin-markdown": "^3.13.6", "typescript": "^4.9.3"}, "peerDependencies": {"puppeteer": "*"}, "lint-staged": {"*.{ts,tsx}": ["prettier --write"]}, "ava": {"extensions": {"ts": "module"}, "nodeArguments": ["--loader=tsx"]}, "keywords": ["openai", "chatgpt", "gpt", "gpt3", "gpt4", "chatbot", "chat", "machine learning", "conversation", "conversational ai", "ai", "ml", "bot"]}