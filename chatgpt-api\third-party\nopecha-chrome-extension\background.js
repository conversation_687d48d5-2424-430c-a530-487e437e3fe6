import{BASE_API,deep_copy,SettingsManager,Time}from"./utils.mjs";import*as bapi from"./api.js";class API{static endpoints={};static register(t,e){var a=t.name+"."+e;const s=t[e];this.endpoints[a]=function(){return s.apply(t,[{tab_id:arguments[0].tab_id,frame_id:arguments[0].frame_id,...arguments[0].data}])}}}class Cache{static cache={};static async set({tab_id:t,name:e,value:a,tab_specific:s}={tab_specific:!1}){return s&&(e=t+"_"+e),Cache.cache[e]=a,Cache.cache[e]}static async get({tab_id:t,name:e,tab_specific:a}={tab_specific:!1}){return a&&(e=t+"_"+e),Cache.cache[e]}static async remove({tab_id:t,name:e,tab_specific:a}={tab_specific:!1}){a&&(e=t+"_"+e);a=Cache.cache[e];return delete Cache.cache[e],a}static async append({tab_id:t,name:e,value:a,tab_specific:s}={tab_specific:!1}){return(e=s?t+"_"+e:e)in Cache.cache||(Cache.cache[e]=[]),Cache.cache[e].push(a),Cache.cache[e]}static async empty({tab_id:t,name:e,tab_specific:a}={tab_specific:!1}){a&&(e=t+"_"+e);a=Cache.cache[e];return Cache.cache[e]=[],a}static async inc({tab_id:t,name:e,tab_specific:a}={tab_specific:!1}){return(e=a?t+"_"+e:e)in Cache.cache||(Cache.cache[e]=0),Cache.cache[e]++,Cache.cache[e]}static async dec({tab_id:t,name:e,tab_specific:a}={tab_specific:!1}){return(e=a?t+"_"+e:e)in Cache.cache||(Cache.cache[e]=0),Cache.cache[e]--,Cache.cache[e]}static async zero({tab_id:t,name:e,tab_specific:a}={tab_specific:!1}){return a&&(e=t+"_"+e),Cache.cache[e]=0,Cache.cache[e]}static{API.register(this,"set"),API.register(this,"get"),API.register(this,"remove"),API.register(this,"append"),API.register(this,"empty"),API.register(this,"inc"),API.register(this,"dec"),API.register(this,"zero")}}class Settings{static data={};static _save(){return new Promise(t=>{bapi.browser.storage.sync.set({settings:Settings.data},t)})}static _get_settings(){return new Promise(e=>{bapi.browser.storage.sync.get(["settings"],({settings:t})=>{e(t)})})}static async load(){for(let t=0;t<4;t++){var e=await Settings._get_settings();if(e)return Settings.data=e,void(Settings.data.version!==SettingsManager.DEFAULT.version&&(e=Settings.data.key,await Settings.reset(),Settings.data.key=e))}await Settings.reset()}static async get(){return Settings.data}static async set({id:t,value:e}){Settings.data[t]=e,await Settings._save()}static async update({settings:t}){for(var[e,a]of Object.entries(t))Settings.data[e]=a;await Settings._save()}static async replace({settings:t}){Settings.data=t,await Settings._save()}static async reset(){Settings.data=deep_copy(SettingsManager.DEFAULT),await Settings._save()}static{API.register(this,"get"),API.register(this,"set"),API.register(this,"update"),API.register(this,"replace"),API.register(this,"reset")}}class Net{static async fetch({url:t,options:e}={options:{}}){try{return await(await fetch(t,e)).text()}catch(t){return null}}static{API.register(this,"fetch")}}class Tab{static reloads={};static _reload({tab_id:e}){return new Promise(t=>bapi.browser.tabs.reload(e,{bypassCache:!0},t))}static async reload({tab_id:t,delay:e,overwrite:a}={delay:0,overwrite:!0}){e=parseInt(e);var s=Tab.reloads[t]?.delay-(Date.now()-Tab.reloads[t]?.start),s=isNaN(s)||s<0?0:s;return!!(a||0==s||e<=s)&&(clearTimeout(Tab.reloads[t]?.timer),Tab.reloads[t]={delay:e,start:Date.now(),timer:setTimeout(()=>Tab._reload({tab_id:t}),e)},!0)}static close({tab_id:e}){return new Promise(t=>bapi.browser.tabs.remove(e,t))}static open({url:e}={url:null}){return new Promise(t=>bapi.browser.tabs.create({url:e},t))}static navigate({tab_id:e,url:a}){return new Promise(t=>bapi.browser.tabs.update(e,{url:a},t))}static info({tab_id:t}){return new Promise(e=>{try{bapi.browser.tabs.get(t,t=>e(t))}catch(t){e(!1)}})}static active(){return new Promise(async e=>{var t;if("firefox"!==bapi.VERSION)return[t]=await bapi.browser.tabs.query({active:!0,lastFocusedWindow:!0}),e(t);bapi.browser.tabs.query({active:!0,lastFocusedWindow:!0},([t])=>{bapi.browser.runtime.lastError,e(t)})})}static{API.register(this,"reload"),API.register(this,"close"),API.register(this,"open"),API.register(this,"navigate"),API.register(this,"info"),API.register(this,"active")}}class Inject{static async _inject(e){e.target.tabId||(t=await Tab.active(),e.target.tabId=t.id);var t=new Promise(t=>bapi.browser.scripting.executeScript(e,t));return t}static async func({tab_id:t,func:e,args:a}={args:[]}){t={target:{tabId:t,allFrames:!0},world:"MAIN",injectImmediately:!0,func:e,args:a};return Inject._inject(t)}static async files({tab_id:t,frame_id:e,files:a}){t={target:{tabId:t,frameIds:[e]},world:"MAIN",injectImmediately:!0,files:a};return"firefox"===bapi.VERSION&&delete t.world,Inject._inject(t)}static{API.register(this,"func"),API.register(this,"files")}}class Recaptcha{static async reset({tab_id:t}){return await Inject.func({tab_id:t,data:{func:()=>{try{window.grecaptcha?.reset()}catch{}},args:[]}}),!0}static{API.register(this,"reset")}}class Server{static ENDPOINT=BASE_API+"/status?v="+bapi.browser.runtime.getManifest().version;static is_fetching_plan=!1;static async get_plan({key:t}){if(Server.is_fetching_plan)return!1;Server.is_fetching_plan=!0;let e={plan:"Unknown",credit:0};try{"undefined"===t&&(t="");var a=await fetch(Server.ENDPOINT+"&key="+t);e=JSON.parse(await a.text())}catch{}return Server.is_fetching_plan=!1,e}static{API.register(this,"get_plan")}}class Image{static encode({url:t}){return new Promise(a=>{fetch(t).then(t=>t.blob()).then(t=>{const e=new FileReader;e.onload=()=>a(e.result),e.readAsDataURL(t)})})}static{API.register(this,"encode")}}class Relay{static async send({tab_id:t,data:e}){t=t||(await Tab.active()).id,bapi.browser.tabs.sendMessage(t,e)}static{API.register(this,"send")}}class Icon{static set({status:a}){return new Promise(t=>{var e="firefox"===bapi.VERSION?bapi.browser.browserAction:bapi.browser.action;"on"===a?e.setIcon({path:{16:"/icon/16.png",32:"/icon/32.png",48:"/icon/48.png",128:"/icon/128.png"}},t):"off"===a?e.setIcon({path:{16:"/icon/16g.png",32:"/icon/32g.png",48:"/icon/48g.png",128:"/icon/128g.png"}},t):t(!1)})}static set_badge_text({tab_id:a,data:s}){return new Promise(t=>{var e={text:s};a&&(e.tabId=a),bapi.browser.action.setBadgeText(e,t)})}static set_badge_color({tab_id:a,data:s}){return new Promise(t=>{var e={color:s};a&&(e.tabId=a),bapi.browser.action.setBadgeBackgroundColor(e,t)})}static async set_badge({tab_id:t,data:{global:e,text:a,color:s}}){t||e||(t=(await Tab.active()).id),e&&(t=null);e=[Icon.set_badge_text({tab_id:t,data:a})];return s&&e.push(Icon.set_badge_color({tab_id:t,data:s})),Promise.all(e)}static{API.register(this,"set")}}class Browser{static async version(){return bapi.VERSION}static async log(){}static{API.register(this,"version"),API.register(this,"log")}}class ContextMenu{static listen(){bapi.browser.contextMenus.onClicked.addListener(function(e,t){if("nopecha_disable_host"===e.menuItemId){e=e.pageUrl;if(e){e=e.replace(/^(.*:)\/\/([A-Za-z0-9\-\.]+)(:[0-9]+)?(.*)$/,"$2");let t=new Set;for(const a of Settings.data.disabled_hosts)t.add(a.trim());t.add(e),t=[...t],Settings.set({id:"disabled_hosts",value:t})}}})}static create(){bapi.browser.contextMenus.create({title:"Disable NopeCHA on this site",id:"nopecha_disable_host"})}static{bapi.browser.runtime.onInstalled.addListener(ContextMenu.create),ContextMenu.listen()}}function listen_setup(){bapi.browser.webRequest.onBeforeRequest.addListener(t=>{try{var e,a,s=t.url.split("#");2<=s.length&&(s.shift(),e="#"+s.join("#"),a=SettingsManager.import(e),Settings.update({settings:a}))}catch(t){}},{urls:["*://*.nopecha.com/setup*"]})}(async()=>{listen_setup(),bapi.register_language(),await Settings.load(),await Icon.set({status:Settings.data.enabled?"on":"off"}),bapi.browser.runtime.onMessage.addListener((t,e,a)=>{const s=t[0];let i=null;t=(i=1<t.length?2===t.length?t[1]:t.slice(1):i)&&"tab_id"in i?i.tab_id:e?.tab?.id,e=e?.frameId;try{API.endpoints[s]({tab_id:t,frame_id:e,data:i}).then(t=>{["Browser.log","Settings.get","Settings.set","Cache.get","Cache.set","Tab.info"].includes(s);try{a(t)}catch(t){}}).catch(t=>{})}catch(t){}return!0})})();
