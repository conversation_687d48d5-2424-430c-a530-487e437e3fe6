"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.runExtensionTests = exports.testConfiguration = exports.testGeneratedCode = exports.mockAPIResponse = void 0;
// Test file to validate extension functionality
const vscode = require("vscode");
// Mock API response for testing
exports.mockAPIResponse = {
    success: true,
    files: [
        {
            filename: 'PricingCard.tsx',
            code: `import React from 'react';
import { motion } from 'framer-motion';

interface PricingCardProps {
  title: string;
  price: string;
  features: string[];
  isPopular?: boolean;
}

export const PricingCard: React.FC<PricingCardProps> = ({
  title,
  price,
  features,
  isPopular = false
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={\`relative p-6 rounded-xl border \${
        isPopular 
          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
          : 'border-gray-200 dark:border-gray-700'
      } backdrop-blur-sm\`}
    >
      {isPopular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
            Most Popular
          </span>
        </div>
      )}
      
      <div className="text-center">
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
          {title}
        </h3>
        <div className="mb-4">
          <span className="text-3xl font-bold text-gray-900 dark:text-white">
            {price}
          </span>
          <span className="text-gray-500 dark:text-gray-400">/month</span>
        </div>
        
        <ul className="space-y-2 mb-6">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center text-gray-600 dark:text-gray-300">
              <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              {feature}
            </li>
          ))}
        </ul>
        
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className={\`w-full py-2 px-4 rounded-lg font-medium transition-colors \${
            isPopular
              ? 'bg-blue-500 hover:bg-blue-600 text-white'
              : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-white'
          }\`}
        >
          Get Started
        </motion.button>
      </div>
    </motion.div>
  );
};`,
            path: 'components/PricingCard.tsx'
        },
        {
            filename: 'usePricing.ts',
            code: `import { useState } from 'react';

export interface PricingPlan {
  id: string;
  title: string;
  price: string;
  features: string[];
  isPopular?: boolean;
}

export const usePricing = () => {
  const [isYearly, setIsYearly] = useState(false);
  
  const plans: PricingPlan[] = [
    {
      id: 'basic',
      title: 'Basic',
      price: isYearly ? '$99' : '$9',
      features: [
        '10 Projects',
        'Basic Support',
        '1GB Storage',
        'Standard Templates'
      ]
    },
    {
      id: 'pro',
      title: 'Pro',
      price: isYearly ? '$199' : '$19',
      features: [
        'Unlimited Projects',
        'Priority Support',
        '10GB Storage',
        'Premium Templates',
        'Advanced Analytics'
      ],
      isPopular: true
    },
    {
      id: 'enterprise',
      title: 'Enterprise',
      price: isYearly ? '$499' : '$49',
      features: [
        'Everything in Pro',
        'Custom Integrations',
        'Unlimited Storage',
        'Dedicated Support',
        'Custom Branding'
      ]
    }
  ];
  
  return {
    plans,
    isYearly,
    setIsYearly,
    toggleBilling: () => setIsYearly(!isYearly)
  };
};`,
            path: 'hooks/usePricing.ts'
        }
    ]
};
// Test function to validate generated code
function testGeneratedCode() {
    try {
        // Test basic syntax validation
        const testCode = exports.mockAPIResponse.files[0].code;
        // Check for balanced brackets
        const openBrackets = (testCode.match(/\{/g) || []).length;
        const closeBrackets = (testCode.match(/\}/g) || []).length;
        if (openBrackets !== closeBrackets) {
            console.error('Unbalanced brackets in generated code');
            return false;
        }
        // Check for React imports
        if (!testCode.includes('import React')) {
            console.error('Missing React import');
            return false;
        }
        // Check for TypeScript interface
        if (!testCode.includes('interface')) {
            console.error('Missing TypeScript interface');
            return false;
        }
        console.log('✅ Generated code validation passed');
        return true;
    }
    catch (error) {
        console.error('❌ Code validation failed:', error);
        return false;
    }
}
exports.testGeneratedCode = testGeneratedCode;
// Test configuration validation
function testConfiguration() {
    try {
        const config = vscode.workspace.getConfiguration('uiorbit');
        // Test that configuration properties exist
        const apiEndpoint = config.get('apiEndpoint');
        const defaultFramework = config.get('defaultFramework');
        if (!apiEndpoint) {
            console.error('Missing API endpoint configuration');
            return false;
        }
        if (!defaultFramework) {
            console.error('Missing default framework configuration');
            return false;
        }
        console.log('✅ Configuration validation passed');
        return true;
    }
    catch (error) {
        console.error('❌ Configuration validation failed:', error);
        return false;
    }
}
exports.testConfiguration = testConfiguration;
// Run all tests
function runExtensionTests() {
    console.log('🧪 Running UIOrbit Extension Tests...');
    const codeTest = testGeneratedCode();
    const configTest = testConfiguration();
    if (codeTest && configTest) {
        console.log('🎉 All tests passed! Extension is ready for use.');
    }
    else {
        console.log('❌ Some tests failed. Please check the issues above.');
    }
}
exports.runExtensionTests = runExtensionTests;
//# sourceMappingURL=test-extension.js.map