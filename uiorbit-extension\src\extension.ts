import * as vscode from 'vscode';
import axios from 'axios';
import * as path from 'path';
import * as fs from 'fs';

interface UIGenerationConfig {
    framework: string;
    styling: string;
    animation: string;
    darkMode: boolean;
    uiStyle: string;
}

interface GeneratedFile {
    filename: string;
    code: string;
    path: string;
}

interface APIResponse {
    success: boolean;
    files: GeneratedFile[];
    error?: string;
}

export function activate(context: vscode.ExtensionContext) {
    console.log('UIOrbit extension is now active!');

    // Register the main command
    let disposable = vscode.commands.registerCommand('uiorbit.generateUI', async () => {
        try {
            await generateUIComponent(context);
        } catch (error) {
            vscode.window.showErrorMessage(`UIOrbit Error: ${error}`);
        }
    });

    context.subscriptions.push(disposable);
}

async function generateUIComponent(context: vscode.ExtensionContext) {
    // Step 1: Get user prompt
    const prompt = await vscode.window.showInputBox({
        prompt: 'Describe the UI component you want to generate',
        placeHolder: 'e.g., "Animated pricing table with glassmorphism and dark mode support"',
        validateInput: (value) => {
            if (!value || value.trim().length === 0) {
                return 'Please enter a description for your UI component';
            }
            return null;
        }
    });

    if (!prompt) {
        return;
    }

    // Step 2: Get configuration options
    const config = await getUIGenerationConfig();
    if (!config) {
        return;
    }

    // Step 3: Validate API key
    const apiKey = await getAPIKey(context);
    if (!apiKey) {
        return;
    }

    // Step 4: Show progress and generate UI
    await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "UIOrbit",
        cancellable: false
    }, async (progress) => {
        progress.report({ increment: 0, message: "Generating UI with AI..." });

        try {
            // Step 5: Call API
            const response = await callUIGenerationAPI(prompt, config, apiKey);
            
            progress.report({ increment: 50, message: "Processing generated code..." });

            // Step 6: Validate and write files
            await validateAndWriteFiles(response.files);
            
            progress.report({ increment: 100, message: "UI component generated successfully!" });

            vscode.window.showInformationMessage(
                `✨ UIOrbit: Generated ${response.files.length} file(s) successfully!`,
                'Open Files'
            ).then(selection => {
                if (selection === 'Open Files') {
                    openGeneratedFiles(response.files);
                }
            });

        } catch (error) {
            handleAPIError(error);
        }
    });
}

async function getUIGenerationConfig(): Promise<UIGenerationConfig | null> {
    const config = vscode.workspace.getConfiguration('uiorbit');
    
    // Get framework
    const framework = await vscode.window.showQuickPick([
        { label: 'React', value: 'react' },
        { label: 'Next.js', value: 'next' }
    ], {
        placeHolder: 'Select framework',
        canPickMany: false
    });

    if (!framework) return null;

    // Get styling system
    const styling = await vscode.window.showQuickPick([
        { label: 'TailwindCSS', value: 'tailwind' },
        { label: 'ShadCN UI', value: 'shadcn' }
    ], {
        placeHolder: 'Select styling system',
        canPickMany: false
    });

    if (!styling) return null;

    // Get animation library
    const animation = await vscode.window.showQuickPick([
        { label: 'Framer Motion', value: 'framer-motion' },
        { label: 'GSAP', value: 'gsap' }
    ], {
        placeHolder: 'Select animation library',
        canPickMany: false
    });

    if (!animation) return null;

    // Get UI style
    const uiStyle = await vscode.window.showQuickPick([
        { label: 'Modern', value: 'modern' },
        { label: 'Glassmorphism', value: 'glassmorphism' },
        { label: 'Neumorphism', value: 'neumorphism' },
        { label: 'Minimalist', value: 'minimalist' }
    ], {
        placeHolder: 'Select UI style',
        canPickMany: false
    });

    if (!uiStyle) return null;

    // Get dark mode preference
    const darkMode = await vscode.window.showQuickPick([
        { label: 'Yes', value: true },
        { label: 'No', value: false }
    ], {
        placeHolder: 'Include dark mode support?',
        canPickMany: false
    });

    if (darkMode === undefined) return null;

    return {
        framework: framework.value,
        styling: styling.value,
        animation: animation.value,
        darkMode: darkMode.value,
        uiStyle: uiStyle.value
    };
}

async function getAPIKey(context: vscode.ExtensionContext): Promise<string | null> {
    // Try to get from secure storage first
    let apiKey = await context.secrets.get('uiorbit.apiKey');
    
    if (!apiKey) {
        // Try to get from configuration
        const config = vscode.workspace.getConfiguration('uiorbit');
        apiKey = config.get('apiKey');
    }

    if (!apiKey) {
        // Prompt user for API key
        apiKey = await vscode.window.showInputBox({
            prompt: 'Enter your UIOrbit API Key',
            placeHolder: 'Get your API key from https://uiorbit.dev/dashboard',
            password: true,
            validateInput: (value) => {
                if (!value || value.trim().length === 0) {
                    return 'API key is required';
                }
                return null;
            }
        });

        if (apiKey) {
            // Store in secure storage
            await context.secrets.store('uiorbit.apiKey', apiKey);
            vscode.window.showInformationMessage('API key saved securely!');
        }
    }

    return apiKey || null;
}

async function callUIGenerationAPI(prompt: string, config: UIGenerationConfig, apiKey: string): Promise<APIResponse> {
    const apiEndpoint = vscode.workspace.getConfiguration('uiorbit').get('apiEndpoint', 'https://api.uiorbit.dev');

    // Development mode - use mock response for testing
    if (apiKey.includes('test-uiorbit-api-key') || apiKey.includes('dev-mode')) {
        vscode.window.showInformationMessage('🧪 Development Mode: Using mock API response');
        return getMockAPIResponse(prompt, config);
    }

    const requestData = {
        prompt,
        config,
        timestamp: Date.now()
    };

    try {
        const response = await axios.post(`${apiEndpoint}/api/generate-ui`, requestData, {
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json',
                'User-Agent': 'UIOrbit-VSCode-Extension/1.0.0'
            },
            timeout: 30000 // 30 second timeout
        });

        return response.data;
    } catch (error: any) {
        if (error.response) {
            throw new Error(`API Error: ${error.response.data?.error || error.response.statusText}`);
        } else if (error.request) {
            throw new Error('Network error: Unable to reach UIOrbit API. Please check your internet connection.');
        } else {
            throw new Error(`Request error: ${error.message}`);
        }
    }
}

async function validateAndWriteFiles(files: GeneratedFile[]): Promise<void> {
    const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
    if (!workspaceFolder) {
        throw new Error('No workspace folder open. Please open a project folder first.');
    }

    for (const file of files) {
        try {
            // Basic TypeScript syntax validation
            if (file.filename.endsWith('.tsx') || file.filename.endsWith('.ts')) {
                validateTypeScriptSyntax(file.code);
            }

            // Create directory if it doesn't exist
            const fullPath = path.join(workspaceFolder.uri.fsPath, file.path);
            const dir = path.dirname(fullPath);
            
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }

            // Write file
            fs.writeFileSync(fullPath, file.code, 'utf8');
            
        } catch (error) {
            throw new Error(`Failed to write file ${file.filename}: ${error}`);
        }
    }
}

function validateTypeScriptSyntax(code: string): void {
    // Basic syntax validation - check for common issues
    const issues: string[] = [];
    
    // Check for unmatched brackets
    const openBrackets = (code.match(/\{/g) || []).length;
    const closeBrackets = (code.match(/\}/g) || []).length;
    if (openBrackets !== closeBrackets) {
        issues.push('Unmatched curly brackets');
    }

    // Check for unmatched parentheses
    const openParens = (code.match(/\(/g) || []).length;
    const closeParens = (code.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
        issues.push('Unmatched parentheses');
    }

    if (issues.length > 0) {
        throw new Error(`Code validation failed: ${issues.join(', ')}`);
    }
}

async function openGeneratedFiles(files: GeneratedFile[]): Promise<void> {
    const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
    if (!workspaceFolder) return;

    for (const file of files.slice(0, 3)) { // Open first 3 files to avoid overwhelming
        const fullPath = path.join(workspaceFolder.uri.fsPath, file.path);
        const uri = vscode.Uri.file(fullPath);
        await vscode.window.showTextDocument(uri);
    }
}

function handleAPIError(error: any): void {
    console.error('UIOrbit API Error:', error);
    
    if (error.message.includes('API Error: 401')) {
        vscode.window.showErrorMessage(
            'Invalid API key. Please check your UIOrbit API key.',
            'Update API Key'
        ).then(selection => {
            if (selection === 'Update API Key') {
                vscode.commands.executeCommand('workbench.action.openSettings', 'uiorbit.apiKey');
            }
        });
    } else if (error.message.includes('API Error: 429')) {
        vscode.window.showWarningMessage(
            'Rate limit exceeded. Please wait a moment before trying again.',
            'Learn More'
        ).then(selection => {
            if (selection === 'Learn More') {
                vscode.env.openExternal(vscode.Uri.parse('https://uiorbit.dev/docs/rate-limits'));
            }
        });
    } else {
        vscode.window.showErrorMessage(`UIOrbit: ${error.message}`);
    }
}

function getMockAPIResponse(prompt: string, config: UIGenerationConfig): APIResponse {
    // Generate contextual mock response based on prompt and config
    const lowerPrompt = prompt.toLowerCase();
    const isCard = lowerPrompt.includes('card');
    const isButton = lowerPrompt.includes('button');
    const isPricing = lowerPrompt.includes('pricing') || lowerPrompt.includes('price');
    const isHero = lowerPrompt.includes('hero');
    const isFooter = lowerPrompt.includes('footer');
    const isNavbar = lowerPrompt.includes('navbar') || lowerPrompt.includes('navigation') || lowerPrompt.includes('nav');
    const isModal = lowerPrompt.includes('modal') || lowerPrompt.includes('popup') || lowerPrompt.includes('dialog');
    const isForm = lowerPrompt.includes('form') || lowerPrompt.includes('input') || lowerPrompt.includes('contact');

    if (isPricing) {
        return {
            success: true,
            files: [
                {
                    filename: 'PricingCard.tsx',
                    code: generatePricingCardCode(config),
                    path: 'components/PricingCard.tsx'
                },
                {
                    filename: 'usePricing.ts',
                    code: generatePricingHookCode(),
                    path: 'hooks/usePricing.ts'
                }
            ]
        };
    } else if (isFooter) {
        return {
            success: true,
            files: [
                {
                    filename: 'AnimatedFooter.tsx',
                    code: generateFooterCode(config, prompt),
                    path: 'components/AnimatedFooter.tsx'
                }
            ]
        };
    } else if (isNavbar) {
        return {
            success: true,
            files: [
                {
                    filename: 'Navbar.tsx',
                    code: generateNavbarCode(config),
                    path: 'components/Navbar.tsx'
                }
            ]
        };
    } else if (isButton) {
        return {
            success: true,
            files: [
                {
                    filename: 'AnimatedButton.tsx',
                    code: generateButtonCode(config),
                    path: 'components/AnimatedButton.tsx'
                }
            ]
        };
    } else if (isHero) {
        return {
            success: true,
            files: [
                {
                    filename: 'HeroSection.tsx',
                    code: generateHeroCode(config),
                    path: 'components/HeroSection.tsx'
                }
            ]
        };
    } else if (isModal) {
        return {
            success: true,
            files: [
                {
                    filename: 'AnimatedModal.tsx',
                    code: generateModalCode(config),
                    path: 'components/AnimatedModal.tsx'
                }
            ]
        };
    } else if (isForm) {
        return {
            success: true,
            files: [
                {
                    filename: 'ContactForm.tsx',
                    code: generateFormCode(config),
                    path: 'components/ContactForm.tsx'
                }
            ]
        };
    } else if (isCard) {
        return {
            success: true,
            files: [
                {
                    filename: 'AnimatedCard.tsx',
                    code: generateCardCode(config),
                    path: 'components/AnimatedCard.tsx'
                }
            ]
        };
    } else {
        // Default: try to infer from prompt
        return {
            success: true,
            files: [
                {
                    filename: 'GeneratedComponent.tsx',
                    code: generateSmartComponent(config, prompt),
                    path: 'components/GeneratedComponent.tsx'
                }
            ]
        };
    }
}

function generateFooterCode(config: UIGenerationConfig, prompt: string): string {
    const hasStars = prompt.toLowerCase().includes('star');
    const hasFloating = prompt.toLowerCase().includes('float');
    const animation = config.animation === 'framer-motion';

    return `import React from 'react';
${animation ? "import { motion } from 'framer-motion';" : ''}

export const AnimatedFooter: React.FC = () => {
  return (
    <footer className="relative bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 text-white overflow-hidden">
      ${hasStars ? `
      {/* Floating Stars Background */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <${animation ? 'motion.div' : 'div'}
            key={i}
            ${animation ? `
            initial={{ opacity: 0, scale: 0 }}
            animate={{
              opacity: [0, 1, 0],
              scale: [0, 1, 0],
              y: [-20, -100],
              x: [0, Math.random() * 100 - 50]
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2
            }}` : ''}
            className="absolute w-1 h-1 bg-white rounded-full animate-pulse"
            style={{
              left: Math.random() * 100 + '%',
              top: Math.random() * 100 + '%',
            }}
          />
        ))}
      </div>` : ''}

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <${animation ? 'motion.div' : 'div'}
            ${animation ? `
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}` : ''}
            className="col-span-1 md:col-span-2"
          >
            <h3 className="text-2xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-indigo-400 bg-clip-text text-transparent">
              Your Company
            </h3>
            <p className="text-gray-300 mb-4 leading-relaxed">
              Building amazing digital experiences with cutting-edge technology and innovative design.
            </p>
            <div className="flex space-x-4">
              {['twitter', 'github', 'linkedin'].map((social, index) => (
                <${animation ? 'motion.a' : 'a'}
                  key={social}
                  ${animation ? `
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}` : ''}
                  href="#"
                  className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-all duration-200 backdrop-blur-sm"
                >
                  <span className="text-sm font-semibold">{social[0].toUpperCase()}</span>
                </${animation ? 'motion.a' : 'a'}>
              ))}
            </div>
          </${animation ? 'motion.div' : 'div'}>

          {/* Quick Links */}
          <${animation ? 'motion.div' : 'div'}
            ${animation ? `
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}` : ''}
          >
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              {['About', 'Services', 'Portfolio', 'Contact'].map((link, index) => (
                <li key={link}>
                  <a href="#" className="text-gray-300 hover:text-white transition-colors duration-200 hover:underline">
                    {link}
                  </a>
                </li>
              ))}
            </ul>
          </${animation ? 'motion.div' : 'div'}>

          {/* Contact Info */}
          <${animation ? 'motion.div' : 'div'}
            ${animation ? `
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}` : ''}
          >
            <h4 className="text-lg font-semibold mb-4">Contact</h4>
            <div className="space-y-2 text-gray-300">
              <p><EMAIL></p>
              <p>+1 (555) 123-4567</p>
              <p>123 Main St, City, State</p>
            </div>
          </${animation ? 'motion.div' : 'div'}>
        </div>

        {/* Bottom Bar */}
        <${animation ? 'motion.div' : 'div'}
          ${animation ? `
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}` : ''}
          className="mt-12 pt-8 border-t border-white/10 flex flex-col md:flex-row justify-between items-center"
        >
          <p className="text-gray-400 text-sm">
            © 2024 Your Company. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
              Privacy Policy
            </a>
            <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
              Terms of Service
            </a>
          </div>
        </${animation ? 'motion.div' : 'div'}>
      </div>
    </footer>
  );
};`;
}

function generatePricingCardCode(config: UIGenerationConfig): string {
    const animation = config.animation === 'framer-motion';

    return `import React from 'react';
${animation ? "import { motion } from 'framer-motion';" : ''}

interface PricingCardProps {
  title: string;
  price: string;
  features: string[];
  isPopular?: boolean;
}

export const PricingCard: React.FC<PricingCardProps> = ({
  title,
  price,
  features,
  isPopular = false
}) => {
  const cardClasses = \`relative p-6 rounded-xl border backdrop-blur-sm transition-all duration-300 \${
    isPopular
      ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 shadow-lg shadow-blue-500/25'
      : 'border-gray-200 dark:border-gray-700 bg-white/80 dark:bg-gray-800/80 hover:shadow-lg'
  }\`;

  const CardComponent = ${animation ? 'motion.div' : 'div'};

  return (
    <CardComponent
      ${animation ? `
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ scale: 1.02, y: -5 }}` : ''}
      className={cardClasses}
    >
      {isPopular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <span className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-1 rounded-full text-sm font-medium shadow-lg">
            ✨ Most Popular
          </span>
        </div>
      )}

      <div className="text-center">
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
          {title}
        </h3>
        <div className="mb-6">
          <span className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
            {price}
          </span>
          <span className="text-gray-500 dark:text-gray-400 ml-1">/month</span>
        </div>

        <ul className="space-y-3 mb-8">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center text-gray-600 dark:text-gray-300">
              <svg className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              {feature}
            </li>
          ))}
        </ul>

        <${animation ? 'motion.button' : 'button'}
          ${animation ? `
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}` : ''}
          className={\`w-full py-3 px-6 rounded-lg font-medium transition-all duration-200 \${
            isPopular
              ? 'bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-lg shadow-blue-500/25'
              : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-white'
          }\`}
        >
          Get Started
        </${animation ? 'motion.button' : 'button'}>
      </div>
    </CardComponent>
  );
};`;
}

function generatePricingHookCode(): string {
    return `import { useState } from 'react';

export interface PricingPlan {
  id: string;
  title: string;
  price: string;
  features: string[];
  isPopular?: boolean;
}

export const usePricing = () => {
  const [isYearly, setIsYearly] = useState(false);

  const plans: PricingPlan[] = [
    {
      id: 'basic',
      title: 'Basic',
      price: isYearly ? '$99' : '$9',
      features: [
        '10 Projects',
        'Basic Support',
        '1GB Storage',
        'Standard Templates'
      ]
    },
    {
      id: 'pro',
      title: 'Pro',
      price: isYearly ? '$199' : '$19',
      features: [
        'Unlimited Projects',
        'Priority Support',
        '10GB Storage',
        'Premium Templates',
        'Advanced Analytics'
      ],
      isPopular: true
    },
    {
      id: 'enterprise',
      title: 'Enterprise',
      price: isYearly ? '$499' : '$49',
      features: [
        'Everything in Pro',
        'Custom Integrations',
        'Unlimited Storage',
        'Dedicated Support',
        'Custom Branding'
      ]
    }
  ];

  return {
    plans,
    isYearly,
    setIsYearly,
    toggleBilling: () => setIsYearly(!isYearly)
  };
};`;
}

function generateButtonCode(config: UIGenerationConfig): string {
    return `import React from 'react';
${config.animation === 'framer-motion' ? "import { motion } from 'framer-motion';" : ''}

interface AnimatedButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
  disabled?: boolean;
}

export const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  onClick,
  disabled = false
}) => {
  const baseClasses = 'font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';

  const variantClasses = {
    primary: 'bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-lg shadow-blue-500/25 focus:ring-blue-500',
    secondary: 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-white focus:ring-gray-500',
    outline: 'border-2 border-blue-500 text-blue-500 hover:bg-blue-500 hover:text-white focus:ring-blue-500'
  };

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };

  const ButtonComponent = ${config.animation === 'framer-motion' ? 'motion.button' : 'button'};

  return (
    <ButtonComponent
      ${config.animation === 'framer-motion' ? `
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}` : ''}
      className={\`\${baseClasses} \${variantClasses[variant]} \${sizeClasses[size]} \${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}\`}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </ButtonComponent>
  );
};`;
}

function generateHeroCode(config: UIGenerationConfig): string {
    return `import React from 'react';
${config.animation === 'framer-motion' ? "import { motion } from 'framer-motion';" : ''}

export const HeroSection: React.FC = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-indigo-900 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
      <div className="absolute top-1/3 right-1/4 w-72 h-72 bg-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>

      <div className="relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto">
        <${config.animation === 'framer-motion' ? 'motion.h1' : 'h1'}
          ${config.animation === 'framer-motion' ? `
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}` : ''}
          className="text-4xl sm:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-gray-900 via-blue-600 to-indigo-600 dark:from-white dark:via-blue-400 dark:to-indigo-400 bg-clip-text text-transparent mb-6"
        >
          Build Amazing UIs
        </${config.animation === 'framer-motion' ? 'motion.h1' : 'h1'}>

        <${config.animation === 'framer-motion' ? 'motion.p' : 'p'}
          ${config.animation === 'framer-motion' ? `
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}` : ''}
          className="text-xl sm:text-2xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed"
        >
          Generate beautiful, responsive components with AI-powered design intelligence
        </${config.animation === 'framer-motion' ? 'motion.p' : 'p'}>

        <${config.animation === 'framer-motion' ? 'motion.div' : 'div'}
          ${config.animation === 'framer-motion' ? `
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}` : ''}
          className="flex flex-col sm:flex-row gap-4 justify-center items-center"
        >
          <button className="px-8 py-4 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-semibold rounded-lg shadow-lg shadow-blue-500/25 transition-all duration-200 transform hover:scale-105">
            Get Started Free
          </button>
          <button className="px-8 py-4 border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-semibold rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200">
            Watch Demo
          </button>
        </${config.animation === 'framer-motion' ? 'motion.div' : 'div'}>
      </div>
    </section>
  );
};`;
}

function generateCardCode(config: UIGenerationConfig): string {
    const animation = config.animation === 'framer-motion';

    return `import React from 'react';
${animation ? "import { motion } from 'framer-motion';" : ''}

interface AnimatedCardProps {
  title: string;
  description: string;
  imageUrl?: string;
  children?: React.ReactNode;
}

export const AnimatedCard: React.FC<AnimatedCardProps> = ({
  title,
  description,
  imageUrl,
  children
}) => {
  const CardComponent = ${animation ? 'motion.div' : 'div'};

  return (
    <CardComponent
      ${animation ? `
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      whileHover={{ y: -5, scale: 1.02 }}` : ''}
      className="group relative p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 backdrop-blur-sm transition-all duration-300 hover:shadow-xl overflow-hidden"
    >
      {imageUrl && (
        <div className="mb-4 -mx-6 -mt-6">
          <img
            src={imageUrl}
            alt={title}
            className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
          />
        </div>
      )}

      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
        {title}
      </h3>
      <p className="text-gray-600 dark:text-gray-300 mb-4 leading-relaxed">
        {description}
      </p>
      {children}

      {/* Hover effect overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
    </CardComponent>
  );
};`;
}

function generateSmartComponent(config: UIGenerationConfig, prompt: string): string {
    const animation = config.animation === 'framer-motion';
    const componentName = 'GeneratedComponent';

    return `import React from 'react';
${animation ? "import { motion } from 'framer-motion';" : ''}

interface ${componentName}Props {
  title?: string;
  description?: string;
  children?: React.ReactNode;
}

export const ${componentName}: React.FC<${componentName}Props> = ({
  title = "Generated Component",
  description = "This component was generated based on: ${prompt}",
  children
}) => {
  const Component = ${animation ? 'motion.div' : 'div'};

  return (
    <Component
      ${animation ? `
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}` : ''}
      className="p-6 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 backdrop-blur-sm"
    >
      <div className="mb-4">
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
          {title}
        </h3>
        <p className="text-gray-600 dark:text-gray-300 text-sm">
          {description}
        </p>
      </div>

      <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
        <p className="text-blue-800 dark:text-blue-200 text-sm">
          💡 This is a smart component generated from your prompt.
          For more specific components, try prompts like "pricing card", "hero section", "footer", etc.
        </p>
      </div>

      {children}
    </Component>
  );
};`;
}

function generateNavbarCode(config: UIGenerationConfig): string {
    const animation = config.animation === 'framer-motion';

    return `import React, { useState } from 'react';
${animation ? "import { motion } from 'framer-motion';" : ''}

export const Navbar: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  const navItems = ['Home', 'About', 'Services', 'Contact'];

  return (
    <nav className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <${animation ? 'motion.div' : 'div'}
            ${animation ? `
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}` : ''}
            className="flex-shrink-0"
          >
            <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              YourLogo
            </h1>
          </${animation ? 'motion.div' : 'div'}>

          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-4">
              {navItems.map((item, index) => (
                <${animation ? 'motion.a' : 'a'}
                  key={item}
                  ${animation ? `
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{ scale: 1.05 }}` : ''}
                  href="#"
                  className="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                >
                  {item}
                </${animation ? 'motion.a' : 'a'}>
              ))}
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 focus:outline-none"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={isOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"} />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isOpen && (
        <${animation ? 'motion.div' : 'div'}
          ${animation ? `
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}` : ''}
          className="md:hidden bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700"
        >
          <div className="px-2 pt-2 pb-3 space-y-1">
            {navItems.map((item) => (
              <a
                key={item}
                href="#"
                className="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200"
              >
                {item}
              </a>
            ))}
          </div>
        </${animation ? 'motion.div' : 'div'}>
      )}
    </nav>
  );
};`;
}

function generateModalCode(config: UIGenerationConfig): string {
    const animation = config.animation === 'framer-motion';

    return `import React from 'react';
${animation ? "import { motion, AnimatePresence } from 'framer-motion';" : ''}

interface AnimatedModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

export const AnimatedModal: React.FC<AnimatedModalProps> = ({
  isOpen,
  onClose,
  title,
  children
}) => {
  if (!isOpen) return null;

  return (
    <${animation ? 'AnimatePresence' : 'div'}>
      <${animation ? 'motion.div' : 'div'}
        ${animation ? `
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}` : ''}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <${animation ? 'motion.div' : 'div'}
          ${animation ? `
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}` : ''}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {title}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Content */}
          <div className="p-6">
            {children}
          </div>

          {/* Footer */}
          <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
              Confirm
            </button>
          </div>
        </${animation ? 'motion.div' : 'div'}>
      </${animation ? 'motion.div' : 'div'}>
    </${animation ? 'AnimatePresence' : 'div'}>
  );
};`;
}

function generateFormCode(config: UIGenerationConfig): string {
    const animation = config.animation === 'framer-motion';

    return `import React, { useState } from 'react';
${animation ? "import { motion } from 'framer-motion';" : ''}

export const ContactForm: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  return (
    <${animation ? 'motion.form' : 'form'}
      ${animation ? `
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}` : ''}
      onSubmit={handleSubmit}
      className="max-w-md mx-auto p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700"
    >
      <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">
        Contact Us
      </h2>

      <div className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Name
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors"
            required
          />
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Email
          </label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors"
            required
          />
        </div>

        <div>
          <label htmlFor="message" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Message
          </label>
          <textarea
            id="message"
            name="message"
            value={formData.message}
            onChange={handleChange}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors resize-none"
            required
          />
        </div>

        <${animation ? 'motion.button' : 'button'}
          ${animation ? `
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}` : ''}
          type="submit"
          className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-lg shadow-blue-500/25"
        >
          Send Message
        </${animation ? 'motion.button' : 'button'}>
      </div>
    </${animation ? 'motion.form' : 'form'}>
  );
};`;
}

export function deactivate() {}
