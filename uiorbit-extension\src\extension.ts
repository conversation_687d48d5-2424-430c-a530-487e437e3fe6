import * as vscode from 'vscode';
import axios from 'axios';
import * as path from 'path';
import * as fs from 'fs';

interface UIGenerationConfig {
    framework: string;
    styling: string;
    animation: string;
    darkMode: boolean;
    uiStyle: string;
}

interface GeneratedFile {
    filename: string;
    code: string;
    path: string;
}

interface APIResponse {
    success: boolean;
    files: GeneratedFile[];
    error?: string;
}

export function activate(context: vscode.ExtensionContext) {
    console.log('UIOrbit extension is now active!');

    // Register the main command
    let disposable = vscode.commands.registerCommand('uiorbit.generateUI', async () => {
        try {
            await generateUIComponent(context);
        } catch (error) {
            vscode.window.showErrorMessage(`UIOrbit Error: ${error}`);
        }
    });

    context.subscriptions.push(disposable);
}

async function generateUIComponent(context: vscode.ExtensionContext) {
    // Step 1: Get user prompt
    const prompt = await vscode.window.showInputBox({
        prompt: 'Describe the UI component you want to generate',
        placeHolder: 'e.g., "Animated pricing table with glassmorphism and dark mode support"',
        validateInput: (value) => {
            if (!value || value.trim().length === 0) {
                return 'Please enter a description for your UI component';
            }
            return null;
        }
    });

    if (!prompt) {
        return;
    }

    // Step 2: Get configuration options
    const config = await getUIGenerationConfig();
    if (!config) {
        return;
    }

    // Step 3: Validate API key
    const apiKey = await getAPIKey(context);
    if (!apiKey) {
        return;
    }

    // Step 4: Show progress and generate UI
    await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "UIOrbit",
        cancellable: false
    }, async (progress) => {
        progress.report({ increment: 0, message: "Generating UI with AI..." });

        try {
            // Step 5: Call API
            const response = await callUIGenerationAPI(prompt, config, apiKey);
            
            progress.report({ increment: 50, message: "Processing generated code..." });

            // Step 6: Validate and write files
            await validateAndWriteFiles(response.files);
            
            progress.report({ increment: 100, message: "UI component generated successfully!" });

            vscode.window.showInformationMessage(
                `✨ UIOrbit: Generated ${response.files.length} file(s) successfully!`,
                'Open Files'
            ).then(selection => {
                if (selection === 'Open Files') {
                    openGeneratedFiles(response.files);
                }
            });

        } catch (error) {
            handleAPIError(error);
        }
    });
}

async function getUIGenerationConfig(): Promise<UIGenerationConfig | null> {
    const config = vscode.workspace.getConfiguration('uiorbit');
    
    // Get framework
    const framework = await vscode.window.showQuickPick([
        { label: 'React', value: 'react' },
        { label: 'Next.js', value: 'next' }
    ], {
        placeHolder: 'Select framework',
        canPickMany: false
    });

    if (!framework) return null;

    // Get styling system
    const styling = await vscode.window.showQuickPick([
        { label: 'TailwindCSS', value: 'tailwind' },
        { label: 'ShadCN UI', value: 'shadcn' }
    ], {
        placeHolder: 'Select styling system',
        canPickMany: false
    });

    if (!styling) return null;

    // Get animation library
    const animation = await vscode.window.showQuickPick([
        { label: 'Framer Motion', value: 'framer-motion' },
        { label: 'GSAP', value: 'gsap' }
    ], {
        placeHolder: 'Select animation library',
        canPickMany: false
    });

    if (!animation) return null;

    // Get UI style
    const uiStyle = await vscode.window.showQuickPick([
        { label: 'Modern', value: 'modern' },
        { label: 'Glassmorphism', value: 'glassmorphism' },
        { label: 'Neumorphism', value: 'neumorphism' },
        { label: 'Minimalist', value: 'minimalist' }
    ], {
        placeHolder: 'Select UI style',
        canPickMany: false
    });

    if (!uiStyle) return null;

    // Get dark mode preference
    const darkMode = await vscode.window.showQuickPick([
        { label: 'Yes', value: true },
        { label: 'No', value: false }
    ], {
        placeHolder: 'Include dark mode support?',
        canPickMany: false
    });

    if (darkMode === undefined) return null;

    return {
        framework: framework.value,
        styling: styling.value,
        animation: animation.value,
        darkMode: darkMode.value,
        uiStyle: uiStyle.value
    };
}

async function getAPIKey(context: vscode.ExtensionContext): Promise<string | null> {
    // Try to get from secure storage first
    let apiKey = await context.secrets.get('uiorbit.apiKey');
    
    if (!apiKey) {
        // Try to get from configuration
        const config = vscode.workspace.getConfiguration('uiorbit');
        apiKey = config.get('apiKey');
    }

    if (!apiKey) {
        // Prompt user for API key
        apiKey = await vscode.window.showInputBox({
            prompt: 'Enter your UIOrbit API Key',
            placeHolder: 'Get your API key from https://uiorbit.dev/dashboard',
            password: true,
            validateInput: (value) => {
                if (!value || value.trim().length === 0) {
                    return 'API key is required';
                }
                return null;
            }
        });

        if (apiKey) {
            // Store in secure storage
            await context.secrets.store('uiorbit.apiKey', apiKey);
            vscode.window.showInformationMessage('API key saved securely!');
        }
    }

    return apiKey || null;
}

async function callUIGenerationAPI(prompt: string, config: UIGenerationConfig, apiKey: string): Promise<APIResponse> {
    const apiEndpoint = vscode.workspace.getConfiguration('uiorbit').get('apiEndpoint', 'https://api.uiorbit.dev');
    
    const requestData = {
        prompt,
        config,
        timestamp: Date.now()
    };

    try {
        const response = await axios.post(`${apiEndpoint}/api/generate-ui`, requestData, {
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json',
                'User-Agent': 'UIOrbit-VSCode-Extension/1.0.0'
            },
            timeout: 30000 // 30 second timeout
        });

        return response.data;
    } catch (error: any) {
        if (error.response) {
            throw new Error(`API Error: ${error.response.data?.error || error.response.statusText}`);
        } else if (error.request) {
            throw new Error('Network error: Unable to reach UIOrbit API. Please check your internet connection.');
        } else {
            throw new Error(`Request error: ${error.message}`);
        }
    }
}

async function validateAndWriteFiles(files: GeneratedFile[]): Promise<void> {
    const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
    if (!workspaceFolder) {
        throw new Error('No workspace folder open. Please open a project folder first.');
    }

    for (const file of files) {
        try {
            // Basic TypeScript syntax validation
            if (file.filename.endsWith('.tsx') || file.filename.endsWith('.ts')) {
                validateTypeScriptSyntax(file.code);
            }

            // Create directory if it doesn't exist
            const fullPath = path.join(workspaceFolder.uri.fsPath, file.path);
            const dir = path.dirname(fullPath);
            
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }

            // Write file
            fs.writeFileSync(fullPath, file.code, 'utf8');
            
        } catch (error) {
            throw new Error(`Failed to write file ${file.filename}: ${error}`);
        }
    }
}

function validateTypeScriptSyntax(code: string): void {
    // Basic syntax validation - check for common issues
    const issues: string[] = [];
    
    // Check for unmatched brackets
    const openBrackets = (code.match(/\{/g) || []).length;
    const closeBrackets = (code.match(/\}/g) || []).length;
    if (openBrackets !== closeBrackets) {
        issues.push('Unmatched curly brackets');
    }

    // Check for unmatched parentheses
    const openParens = (code.match(/\(/g) || []).length;
    const closeParens = (code.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
        issues.push('Unmatched parentheses');
    }

    if (issues.length > 0) {
        throw new Error(`Code validation failed: ${issues.join(', ')}`);
    }
}

async function openGeneratedFiles(files: GeneratedFile[]): Promise<void> {
    const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
    if (!workspaceFolder) return;

    for (const file of files.slice(0, 3)) { // Open first 3 files to avoid overwhelming
        const fullPath = path.join(workspaceFolder.uri.fsPath, file.path);
        const uri = vscode.Uri.file(fullPath);
        await vscode.window.showTextDocument(uri);
    }
}

function handleAPIError(error: any): void {
    console.error('UIOrbit API Error:', error);
    
    if (error.message.includes('API Error: 401')) {
        vscode.window.showErrorMessage(
            'Invalid API key. Please check your UIOrbit API key.',
            'Update API Key'
        ).then(selection => {
            if (selection === 'Update API Key') {
                vscode.commands.executeCommand('workbench.action.openSettings', 'uiorbit.apiKey');
            }
        });
    } else if (error.message.includes('API Error: 429')) {
        vscode.window.showWarningMessage(
            'Rate limit exceeded. Please wait a moment before trying again.',
            'Learn More'
        ).then(selection => {
            if (selection === 'Learn More') {
                vscode.env.openExternal(vscode.Uri.parse('https://uiorbit.dev/docs/rate-limits'));
            }
        });
    } else {
        vscode.window.showErrorMessage(`UIOrbit: ${error.message}`);
    }
}

export function deactivate() {}
