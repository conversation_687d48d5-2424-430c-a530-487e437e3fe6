{"name": "uiorbit", "displayName": "UIOrbit - AI UI/UX Super Copilot", "description": "Generate beautiful, animated, responsive UI components with AI. The ultimate UI/UX copilot for modern frontend development.", "version": "1.0.0", "publisher": "uiorbit", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Snippets", "Machine Learning"], "keywords": ["ui", "react", "tailwind", "ai", "component", "generator", "framer-motion", "shadcn"], "activationEvents": ["onCommand:uiorbit.generateUI"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "uiorbit.generateUI", "title": "UIOrbit: Generate UI Component", "category": "UIOrbit"}], "configuration": {"title": "UIOrbit", "properties": {"uiorbit.apiKey": {"type": "string", "default": "", "description": "Your UIOrbit API Key for authentication", "scope": "application"}, "uiorbit.apiEndpoint": {"type": "string", "default": "https://api.uiorbit.dev", "description": "UIOrbit API endpoint URL", "scope": "application"}, "uiorbit.defaultFramework": {"type": "string", "enum": ["react", "next"], "default": "react", "description": "Default framework for UI generation"}, "uiorbit.defaultStyling": {"type": "string", "enum": ["tailwind", "shadcn"], "default": "tailwind", "description": "Default styling system"}, "uiorbit.defaultAnimation": {"type": "string", "enum": ["framer-motion", "gsap"], "default": "framer-motion", "description": "Default animation library"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}, "dependencies": {"axios": "^1.6.0"}}