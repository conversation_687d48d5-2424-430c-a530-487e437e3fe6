<p>
  Hi everyone - Thank you for your interest in this extension.

Unfortunately, with a sad heart, we made the hard decision to remove the extension from marketplace and discontinue this project for various reasons:

</p>
<ul>
  <li>We were made aware that the extension was being used by some people, against its terms of use in the Disclaimer section and eventually violating OpenAI's <a href="https://openai.com/policies/terms-of-use">Terms of Use (c) Restrictions</a> via the Browser Autologin method. Such issues take the fun out of such hobby project, breaking its original motto 'Your best AI pair programmer'. We do not wish this extension to be affiliated with any such behaviour from now on.</li>
  <li>Unfortunately, vs-code marketplace doesn't have a feature to <a href="https://github.com/microsoft/vsmarketplace/issues/235">remove previous versions</a>. Even if we unpublish the extension, old versions were going to remain online. We have no intention of providing such feature within this product any longer. If being able to remove old versions completely was an option, we would have definitely kept the extension alive since we know so many people love this extension and it became one of their daily interfaces to AI.</li>
  <li>People were confusing this product to be official - though it's mentioned in every documentation possible that, it's only a hobby project that is developed with a couple hours per month/week without any affiliation to OpenAI. The designs(OpenAI logo + svg) remained from the earlier days as-is to keep the amateur, completely free-to-use, seamless integration feeling, which was one of the reasons that led people think it was official. Though most chatgpt tools/integrations, all around the stores(not only limited to vs-code) used the similar identities. It was a mistake huge mistage on our end to keep the OpenAI logos, ChatGPT identity since the beginning, and we apologize for any misunderstandings. However, we must note that **it has been never our intention to gain popularity with it nor capitalize on this.**</li>
  <li>We put so much effort and sleepless nights in the beginning of the project to provide a seamless AI experience within vs-code, when GPT models were not as widespread as today. And from the testimonials we see, we know we achieved our goal to help you level up your developer experience.</li>
  <li>---</li>
  <li>You can download and run the project locally. The instructions to run it will be appended below.</li>
  <li>**The Browser Autologin feature will not be published on this repository due to reasons listed above** to prevent from further Terms of Use violations. If you would like to use ChatGPT on web, please use the official web page to comply with the OpenAI's terms of use. We no longer want to be associated with it or provide a way to people to exploit the motto of this open source code. The rest of the code remained as-is with minimal changes for you to run the code out-of-the-box from this repository.</li>
  <li>The source code will remain on the repo for a while so that people could learn from how this extension worked and ses what it did internally. Open sourcing the project has been the ultimate goal for us instead of trying to monetize this product.</li>
  <li>---</li>
  <li>Our sincere apologies to all, who believed in the future of this product and to those who improved their coding skills within their most loved IDE using the power of OpenAI technologies.</li>
  <li>---</li>
  <li>There are so many amazing projects on Visual Studio Code marketplace, which does the same job, that you could try out.</li>
  <li>This extension was the most loved tool in the recent weeks for a reason. If you are interested in continuing the mission, feel free to do so using its source code. Let us know how it goes!</li>
  <li>We will never publish this extension in any form to public again, but the source code is yours to use however you'd like.</li>
</ul>

### How to run

- Clone the repository to your local machine
- On the root directory, run `yarn` command to install the dependencies listed in `package.json`
- Within VS Code - run the project by simply hitting F5.
- You could also create a 'vsix' package from the source-code and <a href="https://code.visualstudio.com/docs/editor/extension-marketplace#_install-from-a-vsix">install manually</a>.
