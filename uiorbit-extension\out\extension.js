"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
const axios_1 = require("axios");
const path = require("path");
const fs = require("fs");
function activate(context) {
    console.log('UIOrbit extension is now active!');
    // Register the main command
    let disposable = vscode.commands.registerCommand('uiorbit.generateUI', async () => {
        try {
            await generateUIComponent(context);
        }
        catch (error) {
            vscode.window.showErrorMessage(`UIOrbit Error: ${error}`);
        }
    });
    context.subscriptions.push(disposable);
}
exports.activate = activate;
async function generateUIComponent(context) {
    // Step 1: Get user prompt
    const prompt = await vscode.window.showInputBox({
        prompt: 'Describe the UI component you want to generate',
        placeHolder: 'e.g., "Animated pricing table with glassmorphism and dark mode support"',
        validateInput: (value) => {
            if (!value || value.trim().length === 0) {
                return 'Please enter a description for your UI component';
            }
            return null;
        }
    });
    if (!prompt) {
        return;
    }
    // Step 2: Get configuration options
    const config = await getUIGenerationConfig();
    if (!config) {
        return;
    }
    // Step 3: Validate API key
    const apiKey = await getAPIKey(context);
    if (!apiKey) {
        return;
    }
    // Step 4: Show progress and generate UI
    await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "UIOrbit",
        cancellable: false
    }, async (progress) => {
        progress.report({ increment: 0, message: "Generating UI with AI..." });
        try {
            // Step 5: Call API
            const response = await callUIGenerationAPI(prompt, config, apiKey);
            progress.report({ increment: 50, message: "Processing generated code..." });
            // Step 6: Validate and write files
            await validateAndWriteFiles(response.files);
            progress.report({ increment: 100, message: "UI component generated successfully!" });
            vscode.window.showInformationMessage(`✨ UIOrbit: Generated ${response.files.length} file(s) successfully!`, 'Open Files').then(selection => {
                if (selection === 'Open Files') {
                    openGeneratedFiles(response.files);
                }
            });
        }
        catch (error) {
            handleAPIError(error);
        }
    });
}
async function getUIGenerationConfig() {
    const config = vscode.workspace.getConfiguration('uiorbit');
    // Get framework
    const framework = await vscode.window.showQuickPick([
        { label: 'React', value: 'react' },
        { label: 'Next.js', value: 'next' }
    ], {
        placeHolder: 'Select framework',
        canPickMany: false
    });
    if (!framework)
        return null;
    // Get styling system
    const styling = await vscode.window.showQuickPick([
        { label: 'TailwindCSS', value: 'tailwind' },
        { label: 'ShadCN UI', value: 'shadcn' }
    ], {
        placeHolder: 'Select styling system',
        canPickMany: false
    });
    if (!styling)
        return null;
    // Get animation library
    const animation = await vscode.window.showQuickPick([
        { label: 'Framer Motion', value: 'framer-motion' },
        { label: 'GSAP', value: 'gsap' }
    ], {
        placeHolder: 'Select animation library',
        canPickMany: false
    });
    if (!animation)
        return null;
    // Get UI style
    const uiStyle = await vscode.window.showQuickPick([
        { label: 'Modern', value: 'modern' },
        { label: 'Glassmorphism', value: 'glassmorphism' },
        { label: 'Neumorphism', value: 'neumorphism' },
        { label: 'Minimalist', value: 'minimalist' }
    ], {
        placeHolder: 'Select UI style',
        canPickMany: false
    });
    if (!uiStyle)
        return null;
    // Get dark mode preference
    const darkMode = await vscode.window.showQuickPick([
        { label: 'Yes', value: true },
        { label: 'No', value: false }
    ], {
        placeHolder: 'Include dark mode support?',
        canPickMany: false
    });
    if (darkMode === undefined)
        return null;
    return {
        framework: framework.value,
        styling: styling.value,
        animation: animation.value,
        darkMode: darkMode.value,
        uiStyle: uiStyle.value
    };
}
async function getAPIKey(context) {
    // Try to get from secure storage first
    let apiKey = await context.secrets.get('uiorbit.apiKey');
    if (!apiKey) {
        // Try to get from configuration
        const config = vscode.workspace.getConfiguration('uiorbit');
        apiKey = config.get('apiKey');
    }
    if (!apiKey) {
        // Prompt user for API key
        apiKey = await vscode.window.showInputBox({
            prompt: 'Enter your UIOrbit API Key',
            placeHolder: 'Get your API key from https://uiorbit.dev/dashboard',
            password: true,
            validateInput: (value) => {
                if (!value || value.trim().length === 0) {
                    return 'API key is required';
                }
                return null;
            }
        });
        if (apiKey) {
            // Store in secure storage
            await context.secrets.store('uiorbit.apiKey', apiKey);
            vscode.window.showInformationMessage('API key saved securely!');
        }
    }
    return apiKey || null;
}
async function callUIGenerationAPI(prompt, config, apiKey) {
    const apiEndpoint = vscode.workspace.getConfiguration('uiorbit').get('apiEndpoint', 'https://api.uiorbit.dev');
    // Development mode - use mock response for testing
    if (apiKey.includes('test-uiorbit-api-key') || apiKey.includes('dev-mode')) {
        vscode.window.showInformationMessage('🧪 Development Mode: Using mock API response');
        return getMockAPIResponse(prompt, config);
    }
    const requestData = {
        prompt,
        config,
        timestamp: Date.now()
    };
    try {
        const response = await axios_1.default.post(`${apiEndpoint}/api/generate-ui`, requestData, {
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json',
                'User-Agent': 'UIOrbit-VSCode-Extension/1.0.0'
            },
            timeout: 30000 // 30 second timeout
        });
        return response.data;
    }
    catch (error) {
        if (error.response) {
            throw new Error(`API Error: ${error.response.data?.error || error.response.statusText}`);
        }
        else if (error.request) {
            throw new Error('Network error: Unable to reach UIOrbit API. Please check your internet connection.');
        }
        else {
            throw new Error(`Request error: ${error.message}`);
        }
    }
}
async function validateAndWriteFiles(files) {
    const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
    if (!workspaceFolder) {
        throw new Error('No workspace folder open. Please open a project folder first.');
    }
    for (const file of files) {
        try {
            // Basic TypeScript syntax validation
            if (file.filename.endsWith('.tsx') || file.filename.endsWith('.ts')) {
                validateTypeScriptSyntax(file.code);
            }
            // Create directory if it doesn't exist
            const fullPath = path.join(workspaceFolder.uri.fsPath, file.path);
            const dir = path.dirname(fullPath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
            // Write file
            fs.writeFileSync(fullPath, file.code, 'utf8');
        }
        catch (error) {
            throw new Error(`Failed to write file ${file.filename}: ${error}`);
        }
    }
}
function validateTypeScriptSyntax(code) {
    // Basic syntax validation - check for common issues
    const issues = [];
    // Check for unmatched brackets
    const openBrackets = (code.match(/\{/g) || []).length;
    const closeBrackets = (code.match(/\}/g) || []).length;
    if (openBrackets !== closeBrackets) {
        issues.push('Unmatched curly brackets');
    }
    // Check for unmatched parentheses
    const openParens = (code.match(/\(/g) || []).length;
    const closeParens = (code.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
        issues.push('Unmatched parentheses');
    }
    if (issues.length > 0) {
        throw new Error(`Code validation failed: ${issues.join(', ')}`);
    }
}
async function openGeneratedFiles(files) {
    const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
    if (!workspaceFolder)
        return;
    for (const file of files.slice(0, 3)) { // Open first 3 files to avoid overwhelming
        const fullPath = path.join(workspaceFolder.uri.fsPath, file.path);
        const uri = vscode.Uri.file(fullPath);
        await vscode.window.showTextDocument(uri);
    }
}
function handleAPIError(error) {
    console.error('UIOrbit API Error:', error);
    if (error.message.includes('API Error: 401')) {
        vscode.window.showErrorMessage('Invalid API key. Please check your UIOrbit API key.', 'Update API Key').then(selection => {
            if (selection === 'Update API Key') {
                vscode.commands.executeCommand('workbench.action.openSettings', 'uiorbit.apiKey');
            }
        });
    }
    else if (error.message.includes('API Error: 429')) {
        vscode.window.showWarningMessage('Rate limit exceeded. Please wait a moment before trying again.', 'Learn More').then(selection => {
            if (selection === 'Learn More') {
                vscode.env.openExternal(vscode.Uri.parse('https://uiorbit.dev/docs/rate-limits'));
            }
        });
    }
    else {
        vscode.window.showErrorMessage(`UIOrbit: ${error.message}`);
    }
}
function getMockAPIResponse(prompt, config) {
    // Generate contextual mock response based on prompt and config
    const isCard = prompt.toLowerCase().includes('card');
    const isButton = prompt.toLowerCase().includes('button');
    const isPricing = prompt.toLowerCase().includes('pricing');
    const isHero = prompt.toLowerCase().includes('hero');
    if (isPricing || prompt.toLowerCase().includes('price')) {
        return {
            success: true,
            files: [
                {
                    filename: 'PricingCard.tsx',
                    code: generatePricingCardCode(config),
                    path: 'components/PricingCard.tsx'
                },
                {
                    filename: 'usePricing.ts',
                    code: generatePricingHookCode(),
                    path: 'hooks/usePricing.ts'
                }
            ]
        };
    }
    else if (isButton) {
        return {
            success: true,
            files: [
                {
                    filename: 'AnimatedButton.tsx',
                    code: generateButtonCode(config),
                    path: 'components/AnimatedButton.tsx'
                }
            ]
        };
    }
    else if (isHero) {
        return {
            success: true,
            files: [
                {
                    filename: 'HeroSection.tsx',
                    code: generateHeroCode(config),
                    path: 'components/HeroSection.tsx'
                }
            ]
        };
    }
    else {
        // Default card component
        return {
            success: true,
            files: [
                {
                    filename: 'UIComponent.tsx',
                    code: generateDefaultCardCode(config),
                    path: 'components/UIComponent.tsx'
                }
            ]
        };
    }
}
function generatePricingCardCode(config) {
    const animation = config.animation === 'framer-motion' ? 'framer-motion' : 'css';
    const styling = config.styling === 'shadcn' ? 'shadcn' : 'tailwind';
    return `import React from 'react';
${animation === 'framer-motion' ? "import { motion } from 'framer-motion';" : ''}

interface PricingCardProps {
  title: string;
  price: string;
  features: string[];
  isPopular?: boolean;
}

export const PricingCard: React.FC<PricingCardProps> = ({
  title,
  price,
  features,
  isPopular = false
}) => {
  const cardClasses = \`relative p-6 rounded-xl border backdrop-blur-sm transition-all duration-300 \${
    isPopular
      ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 shadow-lg shadow-blue-500/25'
      : 'border-gray-200 dark:border-gray-700 bg-white/80 dark:bg-gray-800/80 hover:shadow-lg'
  } \${config.uiStyle === 'glassmorphism' ? 'backdrop-blur-md bg-white/10 border-white/20' : ''}\`;

  const CardComponent = ${animation === 'framer-motion' ? 'motion.div' : 'div'};

  return (
    <CardComponent
      ${animation === 'framer-motion' ? `
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ scale: 1.02, y: -5 }}` : ''}
      className={cardClasses}
    >
      {isPopular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <span className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-1 rounded-full text-sm font-medium shadow-lg">
            ✨ Most Popular
          </span>
        </div>
      )}

      <div className="text-center">
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
          {title}
        </h3>
        <div className="mb-6">
          <span className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
            {price}
          </span>
          <span className="text-gray-500 dark:text-gray-400 ml-1">/month</span>
        </div>

        <ul className="space-y-3 mb-8">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center text-gray-600 dark:text-gray-300">
              <svg className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              {feature}
            </li>
          ))}
        </ul>

        <${animation === 'framer-motion' ? 'motion.button' : 'button'}
          ${animation === 'framer-motion' ? `
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}` : ''}
          className={\`w-full py-3 px-6 rounded-lg font-medium transition-all duration-200 \${
            isPopular
              ? 'bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-lg shadow-blue-500/25'
              : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-white'
          }\`}
        >
          Get Started
        </${animation === 'framer-motion' ? 'motion.button' : 'button'}>
      </div>
    </CardComponent>
  );
};`;
}
function generatePricingHookCode() {
    return `import { useState } from 'react';

export interface PricingPlan {
  id: string;
  title: string;
  price: string;
  features: string[];
  isPopular?: boolean;
}

export const usePricing = () => {
  const [isYearly, setIsYearly] = useState(false);

  const plans: PricingPlan[] = [
    {
      id: 'basic',
      title: 'Basic',
      price: isYearly ? '$99' : '$9',
      features: [
        '10 Projects',
        'Basic Support',
        '1GB Storage',
        'Standard Templates'
      ]
    },
    {
      id: 'pro',
      title: 'Pro',
      price: isYearly ? '$199' : '$19',
      features: [
        'Unlimited Projects',
        'Priority Support',
        '10GB Storage',
        'Premium Templates',
        'Advanced Analytics'
      ],
      isPopular: true
    },
    {
      id: 'enterprise',
      title: 'Enterprise',
      price: isYearly ? '$499' : '$49',
      features: [
        'Everything in Pro',
        'Custom Integrations',
        'Unlimited Storage',
        'Dedicated Support',
        'Custom Branding'
      ]
    }
  ];

  return {
    plans,
    isYearly,
    setIsYearly,
    toggleBilling: () => setIsYearly(!isYearly)
  };
};`;
}
function generateButtonCode(config) {
    return `import React from 'react';
${config.animation === 'framer-motion' ? "import { motion } from 'framer-motion';" : ''}

interface AnimatedButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
  disabled?: boolean;
}

export const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  onClick,
  disabled = false
}) => {
  const baseClasses = 'font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';

  const variantClasses = {
    primary: 'bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-lg shadow-blue-500/25 focus:ring-blue-500',
    secondary: 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-white focus:ring-gray-500',
    outline: 'border-2 border-blue-500 text-blue-500 hover:bg-blue-500 hover:text-white focus:ring-blue-500'
  };

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };

  const ButtonComponent = ${config.animation === 'framer-motion' ? 'motion.button' : 'button'};

  return (
    <ButtonComponent
      ${config.animation === 'framer-motion' ? `
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}` : ''}
      className={\`\${baseClasses} \${variantClasses[variant]} \${sizeClasses[size]} \${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}\`}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </ButtonComponent>
  );
};`;
}
function generateHeroCode(config) {
    return `import React from 'react';
${config.animation === 'framer-motion' ? "import { motion } from 'framer-motion';" : ''}

export const HeroSection: React.FC = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-indigo-900 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
      <div className="absolute top-1/3 right-1/4 w-72 h-72 bg-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>

      <div className="relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto">
        <${config.animation === 'framer-motion' ? 'motion.h1' : 'h1'}
          ${config.animation === 'framer-motion' ? `
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}` : ''}
          className="text-4xl sm:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-gray-900 via-blue-600 to-indigo-600 dark:from-white dark:via-blue-400 dark:to-indigo-400 bg-clip-text text-transparent mb-6"
        >
          Build Amazing UIs
        </${config.animation === 'framer-motion' ? 'motion.h1' : 'h1'}>

        <${config.animation === 'framer-motion' ? 'motion.p' : 'p'}
          ${config.animation === 'framer-motion' ? `
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}` : ''}
          className="text-xl sm:text-2xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed"
        >
          Generate beautiful, responsive components with AI-powered design intelligence
        </${config.animation === 'framer-motion' ? 'motion.p' : 'p'}>

        <${config.animation === 'framer-motion' ? 'motion.div' : 'div'}
          ${config.animation === 'framer-motion' ? `
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}` : ''}
          className="flex flex-col sm:flex-row gap-4 justify-center items-center"
        >
          <button className="px-8 py-4 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-semibold rounded-lg shadow-lg shadow-blue-500/25 transition-all duration-200 transform hover:scale-105">
            Get Started Free
          </button>
          <button className="px-8 py-4 border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-semibold rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200">
            Watch Demo
          </button>
        </${config.animation === 'framer-motion' ? 'motion.div' : 'div'}>
      </div>
    </section>
  );
};`;
}
function generateDefaultCardCode(config) {
    return `import React from 'react';
${config.animation === 'framer-motion' ? "import { motion } from 'framer-motion';" : ''}

interface UIComponentProps {
  title: string;
  description: string;
  children?: React.ReactNode;
}

export const UIComponent: React.FC<UIComponentProps> = ({
  title,
  description,
  children
}) => {
  const CardComponent = ${config.animation === 'framer-motion' ? 'motion.div' : 'div'};

  return (
    <CardComponent
      ${config.animation === 'framer-motion' ? `
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      whileHover={{ y: -5 }}` : ''}
      className="p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 backdrop-blur-sm transition-all duration-300 hover:shadow-xl"
    >
      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
        {title}
      </h3>
      <p className="text-gray-600 dark:text-gray-300 mb-4">
        {description}
      </p>
      {children}
    </CardComponent>
  );
};`;
}
function deactivate() { }
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map