{"version": 3, "file": "fileWatcher.js", "sourceRoot": "", "sources": ["../src/fileWatcher.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,6BAA6B;AAE7B,iDAA8C;AAE9C,MAAa,WAAW;IAOpB,YACY,OAAgC,EACxC,QAA6B;QADrB,YAAO,GAAP,OAAO,CAAyB;QAPpC,aAAQ,GAA+B,EAAE,CAAC;QAE1C,aAAQ,GAAwB,IAAI,CAAC;QACrC,eAAU,GAAG,KAAK,CAAC;QACnB,kBAAa,GAAa,EAAE,CAAC;QAMjC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,aAAa;QACf,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC3D,IAAI,CAAC,gBAAgB,EAAE;YACnB,OAAO;SACV;QAED,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE;YACnC,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;SACrC;QAED,mBAAmB;QACnB,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;IACxC,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,eAAuC;QAChE,MAAM,aAAa,GAAG,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC;QACjD,IAAI,CAAC,QAAQ,GAAG,IAAI,2BAAY,CAAC,aAAa,CAAC,CAAC;QAEhD,oCAAoC;QACpC,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CACtD,IAAI,MAAM,CAAC,eAAe,CAAC,eAAe,EAAE,sBAAsB,CAAC,CACtE,CAAC;QAEF,4BAA4B;QAC5B,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAC1D,IAAI,MAAM,CAAC,eAAe,CAAC,eAAe,EAAE,iCAAiC,CAAC,CACjF,CAAC;QAEF,eAAe;QACf,SAAS,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YAChC,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,eAAe;QACf,SAAS,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YAChC,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,eAAe;QACf,SAAS,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YAChC,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,sBAAsB;QACtB,aAAa,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YACpC,IAAI,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gBACxC,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;aAC/B;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;IAC9D,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,GAAe,EAAE,UAA6C;QACzF,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC;QAE5B,iCAAiC;QACjC,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE;YACjC,OAAO;SACV;QAED,IAAI;YACA,QAAQ,UAAU,EAAE;gBAChB,KAAK,SAAS,CAAC;gBACf,KAAK,SAAS;oBACV,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;oBAC/B,MAAM;gBACV,KAAK,SAAS;oBACV,MAAM,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;oBACrD,MAAM;aACb;YAED,oBAAoB;YACpB,IAAI,CAAC,eAAe,CAAC,oBAAoB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;SACvE;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,kCAAkC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;SACvE;IACL,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,QAAgB;QACpC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO;SACV;QAED,IAAI;YACA,oBAAoB;YACpB,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC;YAErE,IAAI,UAAU,EAAE;gBACZ,mBAAmB;gBACnB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;gBAE3D,uCAAuC;gBACvC,MAAM,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;gBAErD,oBAAoB;gBACpB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;oBACxB,MAAM,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;iBAC9C;gBAED,OAAO,CAAC,GAAG,CAAC,WAAW,MAAM,CAAC,MAAM,gBAAgB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;aAClF;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,uBAAuB,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;SAC5D;IACL,CAAC;IAEO,KAAK,CAAC,sBAAsB;QAChC,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO;SACV;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,IAAI;YACA,gBAAgB;YAChB,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC7B,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,SAAS;gBAChB,WAAW,EAAE,KAAK;aACrB,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;gBAClB,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC,CAAC;gBAEpE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;oBAChB,OAAO;iBACV;gBAED,yBAAyB;gBACzB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;gBAEpD,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC,CAAC;gBAEtE,2BAA2B;gBAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC3C,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBAC/B,MAAM,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;oBAE3C,kBAAkB;oBAClB,MAAM,eAAe,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;oBAC7D,QAAQ,CAAC,MAAM,CAAC;wBACZ,SAAS,EAAE,eAAe;wBAC1B,OAAO,EAAE,YAAY,KAAK,CAAC,IAAI,KAAK;qBACvC,CAAC,CAAC;iBACN;gBAED,wBAAwB;gBACxB,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;gBACzE,IAAI,aAAa,EAAE;oBACf,MAAM,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,aAAa,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC;iBAClF;gBAED,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,oBAAoB,EAAE,CAAC,CAAC;gBAEnE,0BAA0B;gBAC1B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,uBAAuB,MAAM,CAAC,MAAM,CAAC,MAAM,2BAA2B,CACzE,CAAC;YACN,CAAC,CAAC,CAAC;SACN;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC;SACvE;gBAAS;YACN,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;SAC3B;IACL,CAAC;IAEO,KAAK,CAAC,cAAc;QACxB,sBAAsB;QACtB,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;QACzE,IAAI,aAAa,EAAE;YACf,oFAAoF;YACpF,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;YAC5D,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;SACvC;IACL,CAAC;IAEO,gBAAgB,CAAC,QAAgB;QACrC,MAAM,eAAe,GAAG;YACpB,cAAc;YACd,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;YACP,UAAU;YACV,aAAa;YACb,SAAS;YACT,OAAO;SACV,CAAC;QAEF,MAAM,iBAAiB,GAAG;YACtB,OAAO;YACP,MAAM;YACN,SAAS;YACT,UAAU;SACb,CAAC;QAEF,iBAAiB;QACjB,KAAK,MAAM,OAAO,IAAI,eAAe,EAAE;YACnC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;gBAC5B,OAAO,IAAI,CAAC;aACf;SACJ;QAED,mBAAmB;QACnB,KAAK,MAAM,GAAG,IAAI,iBAAiB,EAAE;YACjC,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBACxB,OAAO,IAAI,CAAC;aACf;SACJ;QAED,iDAAiD;QACjD,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,qBAAqB,CAAC,QAAgB;QAC1C,MAAM,gBAAgB,GAAG;YACrB,cAAc;YACd,eAAe;YACf,eAAe;YACf,oBAAoB;YACpB,oBAAoB;YACpB,gBAAgB;YAChB,gBAAgB;SACnB,CAAC;QAEF,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACzC,OAAO,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAEO,eAAe,CAAC,OAAe;QACnC,mCAAmC;QACnC,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CACnD,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAC/B,GAAG,CACN,CAAC;QAEF,aAAa,CAAC,IAAI,GAAG,OAAO,CAAC;QAC7B,aAAa,CAAC,IAAI,EAAE,CAAC;QAErB,uBAAuB;QACvB,UAAU,CAAC,GAAG,EAAE;YACZ,aAAa,CAAC,IAAI,EAAE,CAAC;YACrB,aAAa,CAAC,OAAO,EAAE,CAAC;QAC5B,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,MAAc,EAAE,QAAgB,CAAC;QACtD,IAAI;YACA,kCAAkC;YAClC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YAEnE,6BAA6B;YAC7B,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YAE9E,0BAA0B;YAC1B,MAAM,SAAS,GAAG,CAAC,GAAG,MAAM,EAAE,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/D,MAAM,YAAY,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,CACzD,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,CACnD,CAAC;YAEF,OAAO,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;SACvC;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO,EAAE,CAAC;SACb;IACL,CAAC;IAED,KAAK,CAAC,iBAAiB;QACnB,IAAI;YACA,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;YACzE,IAAI,CAAC,aAAa,EAAE;gBAChB,OAAO,mBAAmB,CAAC;aAC9B;YAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YACrE,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YACxE,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAE9D,OAAO,YAAY,OAAO,EAAE,SAAS,IAAI,SAAS,SAAS,OAAO,EAAE,OAAO,IAAI,SAAS;cACtF,UAAU,CAAC,MAAM,KAAK,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;SAC7E,KAAK,CAAC,MAAM;YACT,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,eAAe,EAAE,CAAC;SACtD;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,yBAAyB,CAAC;SACpC;IACL,CAAC;IAED,OAAO;QACH,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACpD,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACvB,CAAC;CACJ;AApTD,kCAoTC"}