{"vscode-chatgpt.freeText.title": "ChatGPT: 随便问问", "vscode-chatgpt.clearSession.title": "ChatGPT: 重置会话", "vscode-chatgpt.generateCode.title": "ChatGPT-Codex: 生成代码", "vscode-chatgpt.addTests.title": "ChatGPT: 添加测试", "vscode-chatgpt.findProblems.title": "ChatGPT: 查找错误", "vscode-chatgpt.optimize.title": "ChatGPT: 优化代码", "vscode-chatgpt.explain.title": "ChatGPT: 讲解代码", "vscode-chatgpt.addComments.title": "ChatGPT: 添加评论", "vscode-chatgpt.completeCode.title": "ChatGPT: 完善代码", "vscode-chatgpt.adhoc.title": "ChatGPT: 临时提示", "vscode-chatgpt.customPrompt1.title": "ChatGPT: Prompt1", "vscode-chatgpt.customPrompt2.title": "ChatGPT: Prompt2", "vscode-chatgpt.clearConversation.title": "ChatGPT: 清除对话", "vscode-chatgpt.exportConversation.title": "ChatGPT: 导出对话", "vscode-chatgpt-view-container.name": "对话窗口", "chatgpt.authenticationType.markdownDescription": "选择您的账号类型。 \n\n - `OpenAI Authentication` 标准身份验证方法，使用电子邮件地址登录。 \n\n - `Google Authentication` 使用 Google 账号登录。 \n\n - `Microsoft Authentication` 使用 Microsoft 账号登录", "chatgpt.emailAddress.description": "[可选] 您的 openai.com 邮件账号。如果您希望扩展为你自动登录，请提供此信息。此扩展不会收集你的信息，但您的配置将以明文形式储存在 Vscode 配置文件中，依然存在泄漏风险。如果您想手动填写密码以保护隐私安全，请不要在此填写信息。", "chatgpt.password.description": "[可选] 您的 openai.com 登录密码。如果您希望扩展为你自动登录，请提供此信息。此扩展不会收集你的信息，但您的配置将以明文形式储存在 Vscode 配置文件中，依然存在泄漏风险。如果您想手动填写密码以保护隐私安全，请不要在此填写信息。", "chatgpt.proxyServer.markdownDescription": "[可选] 对于浏览器登录方式，您可以指定代理服务器。仅支持 HTTP 代理，直接填地址，不需要提供协议。格式示例：\n\n `authenticated`:`myUsername:<EMAIL>:3001` \n\n `anonymous`: `**************:999` \n\n **注意: 仅适用于 `浏览器自动登录` 方式**", "chatgpt.chromiumPath.markdownDescription": "[可选] 对于浏览器登录方式，您可以提供 Chromium 内核的浏览器的可执行文件路径。例如 `Chrome`、`Edge` 等。\n\n除非您更改了浏览器安装路径，否则我们使用每个操作系统的默认路径来使用 `Chrome`。\n\n **Windows**\n C:\\\\Program Files\\\\Google\\\\Chrome\\\\Application\\\\chrome.exe \n\n **MAC**\n /Applications/Google Chrome.app/Contents/MacOS/Google Chrome \n\n 您也可以使用基于 Chromium 的 Edge。例如：\n\n **Windows Edge**\n C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe \n\n **如果您想知道当前系统默认的浏览器路径，可通过访问此链接找到：`chrome://version` 或 `edge://version`**", "chatgpt.gpt3.generateCode-enabled.description": "选中代码后，在上下文菜单选项显示[生成代码或注释]。仅适用于 code-* 语言模型。", "chatgpt.promptPrefix.addTests.default": "测试以下代码功能：", "chatgpt.promptPrefix.addTests.description": "允许您选中代码后，让 GPT 针对此代码提供测试的快速 Prompt 前缀。", "chatgpt.promptPrefix.addTests-enabled.description": "选中代码后，是否在上下文菜单选项显示[添加测试]", "chatgpt.promptPrefix.findProblems.default": "找出以下代码存在问题：", "chatgpt.promptPrefix.findProblems.description": "允许您选中代码后，让 GPT 针对此代码查找问题的快速 Prompt 前缀", "chatgpt.promptPrefix.findProblems-enabled.description": "选中代码后，是否在上下文菜单选项显示[查找问题]", "chatgpt.promptPrefix.optimize.default": "优化以下代码，使代码更简洁，容易理解：", "chatgpt.promptPrefix.optimize.description": "允许您选中代码后，让 GPT 针对此代码进行优化的快速 Prompt 前缀。", "chatgpt.promptPrefix.optimize-enabled.description": "选中代码后，是否在上下文菜单选项显示[优化代码]", "chatgpt.promptPrefix.explain.default": "解释以下代码作用：", "chatgpt.promptPrefix.explain.description": "允许您选中代码后，让 GPT 针对此代码分析功能的快速 Prompt 前缀。", "chatgpt.promptPrefix.explain-enabled.description": "选中代码后，是否在上下文菜单选项显示[解释代码]", "chatgpt.promptPrefix.addComments.default": "为以下代码添加注释：", "chatgpt.promptPrefix.addComments.description": "允许您选中代码后，让 GPT 针对此代码添加注释的快速 Prompt 前缀。", "chatgpt.promptPrefix.addComments-enabled.description": "选中代码后，是否在上下文菜单选项显示[添加注释]", "chatgpt.promptPrefix.completeCode.default": "补全以下的代码：", "chatgpt.promptPrefix.completeCode.description": "允许您选中代码后，让 GPT 针对此代码补全代码的快速 Prompt 前缀。", "chatgpt.promptPrefix.completeCode-enabled.description": "选中代码后，是否在上下文菜单选项显示[补全代码]", "chatgpt.promptPrefix.customPrompt1.description": "您的自定义Prompt提示。默认情况下禁用，需要手动勾选启用。", "chatgpt.promptPrefix.customPrompt1-enabled.markdownDescription": "用于您的自定义 Prompt 的提示前缀，默认情况下为空，如需启用，需先启用功能。", "chatgpt.promptPrefix.customPrompt2.description": "您的自定义Prompt提示。默认情况下禁用，需要手动勾选启用。", "chatgpt.promptPrefix.customPrompt2-enabled.markdownDescription": "用于您的自定义 Prompt 的提示前缀，默认情况下为空，如需启用，需先启用功能。", "chatgpt.promptPrefix.adhoc-enabled.description": "在上下文菜单中启用用于选定代码的 adhoc 命令的提示前缀", "chatgpt.gpt3.apiKey.markdownDescription": "OpenAI API 密钥。[从 OpenAI 获取您的 API 密钥](https://beta.openai.com/account/api-keys)。\n\n **仅适用于 OpenAI API Key 方式使用此设置。**", "chatgpt.gpt3.apiBaseUrl.markdownDescription": "[可选] OpenAI API 访问路径。如果您自定义它，请确保具有相同的格式。以 `https://` 开头，没有尾随斜杠。完整性端点后缀在内部添加，例如供参考：`${apiBaseUrl}/v1/completions`", "chatgpt.gpt3.organization.markdownDescription": "[可选] OpenAI 组织 ID。[文档](https://beta.openai.com/docs/api-reference/requesting-organization)。\n\n**仅适用于 OpenAI API Key 方式使用此设置。**", "chatgpt.gpt3.model.markdownDescription": "用于 Prompt 的 OpenAI 语言模型。[文档](https://beta.openai.com/docs/models/models)。\n\n**如果出现 400 Bad Request，请检查您的登录方式是否允许使用此语言模型。**", "chatgpt.gpt3.model.enumItemLabels5": "OpenAI API Key - gpt-3.5-turbo", "chatgpt.gpt3.model.enumItemLabels6": "OpenAI API Key - gpt-3.5-turbo-0301", "chatgpt.gpt3.model.enumItemLabels7": "OpenAI API Key - text-davinci-003", "chatgpt.gpt3.model.enumItemLabels8": "OpenAI API Key - text-curie-001", "chatgpt.gpt3.model.enumItemLabels9": "OpenAI API Key - text-babbage-001", "chatgpt.gpt3.model.enumItemLabels10": "OpenAI API Key - text-ada-001", "chatgpt.gpt3.model.enumItemLabels11": "OpenAI API Key - code-davinci-002", "chatgpt.gpt3.model.enumItemLabels12": "OpenAI API Key - code-cushman-001", "chatgpt.gpt3.model.markdownEnumDescriptions1": "免费版 ChatGPT 模型，用于 chat.openai.com。", "chatgpt.gpt3.model.markdownEnumDescriptions2": "ChatGPT Plus GPT-4，用于 chat.openai.com。需要订阅 ChatGPT Plus。\n\n**比 GPT-3.5 模型更胜一筹，能够执行更复杂的任务，并针对聊天进行优化，但生成速度较慢。**", "chatgpt.gpt3.model.markdownEnumDescriptions3": "ChatGPT Plus default model，用于 chat.openai.com。需要订阅 ChatGPT Plus。\n\n**针对处理速度方面进行了优化。**", "chatgpt.gpt3.model.markdownEnumDescriptions4": "ChatGPT Plus legacy model，用于 chat.openai.com。需要订阅 ChatGPT Plus。\n\n**上一代 ChatGPT Plus 的语言模型，不推荐使用。**", "chatgpt.gpt3.model.markdownEnumDescriptions5": "最强大的 GPT-3.5 模型，并以 1/10 的成本进行聊天优化，而 `text-davinci-003` 的成本是它的十倍。将使用我们的最新模型迭代进行更新。", "chatgpt.gpt3.model.markdownEnumDescriptions6": "来自 2023 年 3 月 1 日的 `gpt-3.5-turbo` 快照。与 `gpt-3.5-turbo` 不同的是，该模型不会接收更新，并且仅支持三个月，到 2023 年 6 月 1 日结束。", "chatgpt.gpt3.maxTokens.markdownDescription": "生成结果的最大令牌数。\n\n您的 Prompt 令牌数加上 max_tokens 的数量不能超过语言模型的上下文长度。大多数模型的上下文长度为 2048 个令牌（最新的模型除外，其支持 4096 个令牌）。[文档](https://beta.openai.com/docs/api-reference/completions/create#completions/create-max_tokens) \n\n**仅适用于 OpenAI API Key 方式使用此设置。**", "chatgpt.gpt3.temperature.markdownDescription": "模型的语言温度采样。较高的值意味着模型将更具有创造性以及个性化，尝试使用 0.9 让语气更加活泼。使用 0 的结果可以更加准确，适用于需要明确定义答案的应用(使用 rgmax 采样)。\n\n建议修改此设置或top_p但不是两个都修改。[文档](https://beta.openai.com/docs/api-reference/completions/create#completions/create-temperature)\n\n **仅适用于 OpenAI API Key 方式使用此设置。**", "chatgpt.gpt3.top_p.markdownDescription": "与温度采样相反的选择，称为 nucleus 采样，模型考虑 top_p 概率质量的 token 的结果。因此，0.1 表示只考虑组成前 10% 概率质量的 token。\n\n建议修改此项或温度，但不要同时修改两项。[文档](https://beta.openai.com/docs/api-reference/completions/create#completions/create-top_p) \n\n**仅适用于 OpenAI API Key 方式使用此设置。**", "chatgpt.response.showNotification.description": "是否允许 ChatGPT 机器人回答您的问题时接收通知。", "chatgpt.response.autoScroll.description": "每当对话窗口添加新问题或响应时，扩展程序将自动滚动到底部。您可以通过禁用此设置来更改该行为。", "chatgpt.telemetry.disable.markdownDescription": "禁用扩展收集必要的信息。此扩展尊重您默认的 VS Code Telemetry 设置，我们将检查两个必要设置。**重要**：我们不跟踪用户数据，我们只使用 Telemetry 检查哪些功能被使用，哪些没有使用。这使我们能够做出关于扩展未来应该添加或增强哪些功能的准确决策。"}