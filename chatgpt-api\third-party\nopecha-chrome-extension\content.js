function sleep(t){return new Promise(e=>setTimeout(t))}class BG{static exec(){return new Promise(t=>{try{chrome.runtime.sendMessage([...arguments],t)}catch(e){sleep(1e3).then(()=>{t(null)})}})}}class Net{static async fetch(e,t){return BG.exec("Net.fetch",{url:e,options:t})}}class Script{static inject_file(a){return new Promise(e=>{var t=document.createElement("script");t.src=chrome.runtime.getURL(a),t.onload=e,(document.head||document.documentElement).appendChild(t)})}}class Location{static parse_hostname(e){return e.replace(/^(.*:)\/\/([A-Za-z0-9\-\.]+)(:[0-9]+)?(.*)$/,"$2")}static async hostname(){var e=await BG.exec("Tab.info"),e=e.url||"Unknown Host";return Location.parse_hostname(e)}}class Image{static encode(t){return new Promise(a=>{if(null===t)return a(null);const e=new XMLHttpRequest;e.onload=()=>{const t=new FileReader;t.onloadend=()=>{let e=t.result;if(e.startsWith("data:text/html;base64,"))return a(null);e=e.replace("data:image/jpeg;base64,",""),a(e)},t.readAsDataURL(e.response)},e.onerror=()=>{a(null)},e.onreadystatechange=()=>{4==this.readyState&&200!=this.status&&a(null)},e.open("GET",t),e.responseType="blob",e.send()})}}class NopeCHA{static INFERENCE_URL="https://dev-api.nopecha.com";static MAX_WAIT_POST=60;static MAX_WAIT_GET=60;static ERRORS={UNKNOWN:9,INVALID_REQUEST:10,RATE_LIIMTED:11,BANNED_USER:12,NO_JOB:13,INCOMPLETE_JOB:14,INVALID_KEY:15,NO_CREDIT:16,UPDATE_REQUIRED:17};static async post({captcha_type:e,task:t,image_urls:a,image_data:r,grid:n,audio_data:o,key:i}){for(var s=Date.now(),c=await BG.exec("Tab.info");!(Date.now()-s>1e3*NopeCHA.MAX_WAIT_POST);){var l={type:e,task:t,key:i,v:chrome.runtime.getManifest().version,url:c?c.url:window.location.href};a&&(l.image_urls=a),r&&(l.image_data=r),n&&(l.grid=n),o&&(l.audio_data=o);try{var d={"Content-Type":"application/json"},u=(i&&"undefined"!==i&&(d.Authorization="Bearer "+i),await Net.fetch(BASE_API,{method:"POST",headers:d,body:JSON.stringify(l)})),p=JSON.parse(u);if("error"in p){if(p.error===NopeCHA.ERRORS.RATE_LIMITED){await Time.sleep(2e3);continue}if(p.error===NopeCHA.ERRORS.INVALID_KEY)break;if(p.error===NopeCHA.ERRORS.NO_CREDIT)break;break}var _=p.data;return await NopeCHA.get({job_id:_,key:i})}catch(e){}}return{job_id:null,data:null}}static async get({job_id:e,key:t}){for(var a=Date.now();!(Date.now()-a>1e3*NopeCHA.MAX_WAIT_GET);){await Time.sleep(1e3);var r={},r=(t&&"undefined"!==t&&(r.Authorization="Bearer "+t),await Net.fetch(BASE_API+`?id=${e}&key=`+t,{headers:r}));try{var n=JSON.parse(r);if(!("error"in n))return{job_id:e,data:n.data,metadata:n.metadata};if(n.error!==NopeCHA.ERRORS.INCOMPLETE_JOB)return{job_id:e,data:null,metadata:null}}catch(e){}}return{job_id:e,data:null,metadata:null}}}
